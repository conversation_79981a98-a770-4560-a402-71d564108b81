import {
  Logo,
  NavbarTwoColumns,
  NavMenu,
  NavMenuItem,
} from 'astro-boilerplate-components';
import { CustomSection } from '@/components/CustomSection';

const Navbar = () => (
  <CustomSection>
    <NavbarTwoColumns>
      <a href="/">
        <Logo
          icon={
            <svg
              className="mr-1 h-10 w-10 stroke-cyan-600"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M0 0h24v24H0z" stroke="none"></path>
              <rect x="3" y="12" width="6" height="8" rx="1"></rect>
              <rect x="9" y="8" width="6" height="12" rx="1"></rect>
              <rect x="15" y="4" width="6" height="16" rx="1"></rect>
              <path d="M4 20h14"></path>
            </svg>
          }
          name="Software Development Simplified"
        />
      </a>

      <NavMenu>
        <NavMenuItem href="/posts/">Blog</NavMenuItem>
        <NavMenuItem href="/tags/">Tags</NavMenuItem>
        <NavMenuItem href="https://github.com/dariogriffo">GitHub</NavMenuItem>
        <NavMenuItem href="https://twitter.com/dariogriffo">
          Twitter
        </NavMenuItem>
        <li>
          <a href="/search/" className="hover:text-cyan-400">
            <span className="flex items-center">
              <svg
                className="h-6 w-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
              <span className="sr-only">Search</span>
            </span>
          </a>
        </li>
      </NavMenu>
    </NavbarTwoColumns>
  </CustomSection>
);

export { Navbar };
