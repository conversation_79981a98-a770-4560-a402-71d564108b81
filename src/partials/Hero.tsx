import {
  GradientText,
  Section,
} from 'astro-boilerplate-components';
import ResponsiveImage from '../components/ResponsiveImage';
import ResponsiveHeroSocial from '../components/ResponsiveHeroSocial';
import ResponsiveHeroAvatar from '../components/ResponsiveHeroAvatar';

const Hero = () => (
  <CustomSection>
    <ResponsiveHeroAvatar
      title={
        <>
          Hi there, I'm <GradientText>Dario</GradientText> 👋
        </>
      }
      description={
        <>
          I'm a Tech Leader & Staff Engineer specializing in building scalable,
          event-driven platforms and distributed systems. With extensive
          experience in payment systems, securities management, and enterprise
          solutions, I focus on sharing insights and{' '}
          <a className="text-cyan-600 font-semibold hover:underline hover:text-cyan-500" href="https://github.com/dariogriffo">
            building robust architectures
          </a>{' '}
          using .NET, AWS, and event-driven patterns. I'm a passionate Debian GNU/Linux
          user and advocate for open-source technologies.
        </>
      }
      avatar={
        <ResponsiveImage
          src="/assets/images/avatar.png"
          className="h-80 w-64"
          alt="Avatar"
          loading="lazy"
        />
      }
      socialButtons={
        <>
          <a href="https://twitter.com/dariogriffo">
            <ResponsiveHeroSocial
              src="/assets/images/twitter-icon.png"
              alt="Twitter icon"
            />
          </a>
          <a href="https://linkedin.com/in/dariogriffo">
            <ResponsiveHeroSocial
              src="/assets/images/linkedin-icon.png"
              alt="LinkedIn icon"
            />
          </a>
          <a href="https://github.com/dariogriffo">
            <ResponsiveHeroSocial
              src="/assets/images/github-icon.png"
              alt="GitHub icon"
            />
          </a>
          <a href="https://youtube.com/@dariogriffo">
            <ResponsiveHeroSocial
              src="/assets/images/youtube-icon.png"
              alt="YouTube icon"
            />
          </a>
        </>
      }
    />
  </CustomSection>
);

export { Hero };
