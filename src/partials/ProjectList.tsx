import {
  GradientText,
} from 'astro-boilerplate-components';
import { CustomSection } from '@/components/CustomSection';
import Tags, { ColorTags } from '../components/Tags';
import ResponsiveProject from '../components/ResponsiveProject';

const ProjectList = () => (
  <CustomSection
    title={
      <>
        Open Source <GradientText>Projects</GradientText>
      </>
    }
  >
    <div className="flex flex-col gap-6">
      <ResponsiveProject
        name="Debian Packaging Repository"
        description="A collection of Debian packages for tools not available in the official Debian repositories. This project includes packages for popular developer tools like lazydocker, yazi, and lowfi, making them easily installable on Debian-based systems through a custom APT repository."
        link="https://debian.griffo.io"
        img={{
          src: '/assets/images/debian.png',
          alt: 'Debian Packaging Repository',
        }}
        category={
          <>
            <Tags color={ColorTags.INDIGO}>Debian</Tags>
            <Tags color={ColorTags.EMERALD}>Linux</Tags>
            <Tags color={ColorTags.AMBER}>Packaging</Tags>
            <Tags color={ColorTags.VIOLET}>Open Source</Tags>
          </>
        }
      />
      <ResponsiveProject
        name="Configuration.Extensions.EnvironmentFile"
        description="A .NET library that enables Unix-style environment file configuration for .NET Core applications. It allows for local development environments that closely mirror production setups, supporting multiple files, variable prefixes, and automatic configuration updates."
        link="https://github.com/dariogriffo/Configuration.Extensions.EnvironmentFile"
        img={{
          src: '/assets/images/repos/configuration_extensions_environment_file.png',
          alt: 'Environment File Configuration',
        }}
        category={
          <>
            <Tags color={ColorTags.ROSE}>.NET Core</Tags>
            <Tags color={ColorTags.SKY}>Configuration</Tags>
            <Tags color={ColorTags.LIME}>DevOps</Tags>
            <Tags color={ColorTags.FUCHSIA}>C#</Tags>
          </>
        }
      />
      <ResponsiveProject
        name="ServiceCollection.Extensions.Modules"
        description="A modular approach to Microsoft.Extensions.DependencyInjection, allowing for better organization of service registration through modules. Supports nested modules, parameterized modules, and module registration via actions."
        link="https://github.com/dariogriffo/ServiceCollection.Extensions.Modules"
        img={{
          src: '/assets/images/repos/service_collection_extensions_modules.png',
          alt: 'Service Collection Modules',
        }}
        category={
          <>
            <Tags color={ColorTags.ROSE}>.NET Core</Tags>
            <Tags color={ColorTags.EMERALD}>DI</Tags>
            <Tags color={ColorTags.FUCHSIA}>C#</Tags>
            <Tags color={ColorTags.INDIGO}>Modules</Tags>
          </>
        }
      />
      <ResponsiveProject
        name="Registrator.NET"
        description="A .NET service registration library that simplifies dependency injection setup. It provides a clean and efficient way to register services in your .NET applications while maintaining flexibility and control."
        link="https://github.com/dariogriffo/registrator-net"
        img={{
          src: '/assets/images/repos/registrator_net.png',
          alt: 'Registrator.NET',
        }}
        category={
          <>
            <Tags color={ColorTags.FUCHSIA}>.NET Core</Tags>
            <Tags color={ColorTags.INDIGO}>DI</Tags>
            <Tags color={ColorTags.ROSE}>C#</Tags>
          </>
        }
      />
    </div>
  </CustomSection>
);

export { ProjectList };
