---
import { <PERSON> } from '@/partials/Hero';
import { ProjectList } from '@/partials/ProjectList';
import { RecentPosts } from '@/partials/RecentPosts';
import Base from '@/templates/Base.astro';
import { AppConfig } from '@/utils/AppConfig';
import { sortByDate } from '@/utils/Posts';
import { CustomBlogGallery } from '@/components/CustomBlogGallery';

const { title } = AppConfig;
const { description } = AppConfig;

// Get blog posts
const unsortedPosts = await Astro.glob<any>('./posts/*.md');
const posts = sortByDate(unsortedPosts);
const recentPosts = posts.slice(0, 3); // Get 3 most recent posts
---

<Base head={{ title, description }}>
  <Hero />
  <ProjectList />
  <RecentPosts postList={recentPosts} />
  <!-- CTA temporarily hidden until content is ready -->
  <!-- <CTA /> -->
</Base>
