---
import type { GetStaticPathsOptions, MarkdownInstance } from 'astro';
import type { IFrontmatter } from 'astro-boilerplate-components';
import {
  NewerOlderPagination,
  PaginationHeader,
  Section,
} from 'astro-boilerplate-components';
import { CustomBlogGallery } from '@/components/CustomBlogGallery';

import { CTA } from '@/partials/CTA';
import Base from '@/templates/Base.astro';
import { AppConfig } from '@/utils/AppConfig';
import { sortByDate } from '@/utils/Posts';

interface Props {
  page: {
    data: MarkdownInstance<IFrontmatter>[];
    start: number;
    end: number;
    total: number;
    currentPage: number;
    size: number;
    lastPage: number;
    url: {
      current: string;
      prev: string | undefined;
      next: string | undefined;
    };
  };
}

export const prerender = true;

export async function getStaticPaths({ paginate }: GetStaticPathsOptions) {
  const allPosts = await Astro.glob<IFrontmatter>('./*.md');
  const sortedPosts = sortByDate(allPosts);

  // Ensure we return at least one page even if there are no posts
  if (sortedPosts.length === 0) {
    return [
      {
        params: { page: 1 },
        props: {
          page: {
            data: [],
            start: 0,
            end: 0,
            total: 0,
            currentPage: 1,
            size: 6,
            lastPage: 1,
            url: {
              current: '/posts/1',
              prev: undefined,
              next: undefined,
            },
          },
        },
      },
    ];
  }

  return paginate(sortedPosts, { pageSize: 6 });
}

const { page } = Astro.props as Props;
const titleSuffix = ` - ${AppConfig.site_name}`;

// Add fallback for page object in case it's undefined
const currentPage = page?.currentPage || 1;
const titleBase = `Blog page ${currentPage}`;
const title = titleBase + titleSuffix;
const description = 'Blog posts listing page';

// Create a default page object if it's undefined
const safePage = page || {
  data: [],
  start: 0,
  end: 0,
  total: 0,
  currentPage: 1,
  size: 6,
  lastPage: 1,
  url: {
    current: '/posts/1',
    prev: undefined,
    next: undefined,
  },
};
---

<Base head={{ title, description }}>
  <Section>
    <PaginationHeader
      title='Blog Posts'
      description={safePage.data.length === 0
        ? 'No published posts yet.'
        : `Showing ${safePage.start + 1}-${safePage.end} of ${safePage.total} posts`}
    />
  </Section>

  <Section>
    <CustomBlogGallery postList={safePage.data} />
  </Section>

  {safePage.total > 0 && (
    <Section>
      <NewerOlderPagination page={safePage} />
    </Section>
  )}

  {/* CTA temporarily hidden - uncomment when ready to show newsletter subscription */}
  {/* <CTA /> */}
</Base>
