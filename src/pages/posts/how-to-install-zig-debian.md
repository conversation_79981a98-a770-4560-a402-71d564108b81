---
layout: '@/templates/BasePost.astro'
title: 'How to Install Zig on Debian: The Easy Way with debian.griffo.io'
description: 'Learn how to easily install Zig programming language on Debian using the unofficial debian.griffo.io repository, with both stable and nightly builds available.'
pubDate: 2025-01-28T10:00:00Z
imgSrc: '/assets/images/zig-debian-install.png'
imgAlt: 'Zig Programming Language Installation on Debian'
tags: ['Zig', 'Debian', 'Linux', 'Programming', 'Installation']
---

# How to Install Zig on Debian: The Easy Way with debian.griffo.io

[Zig](https://ziglang.org/) is a general-purpose programming language and toolchain designed for maintaining robust, optimal, and reusable software. While Zig is gaining popularity among developers for its performance and safety features, installing it on Debian systems can be challenging since it's not available in the official Debian repositories.

Fortunately, there's an easier way: using the unofficial `debian.griffo.io` repository, which provides pre-built Debian packages for Zig and many other useful development tools.

## Why Use debian.griffo.io?

The `debian.griffo.io` repository offers several advantages over manual installation:

- **Easy installation and updates** through APT package manager
- **Both stable and nightly builds** available
- **Automatic dependency management**
- **Consistent with Debian packaging standards**
- **Supports multiple Debian distributions** (Bookworm, Trixie, Sid)

## Prerequisites

Before we begin, make sure you have:

- A Debian-based system (Bookworm, Trixie, or Sid)
- `sudo` privileges
- `curl` installed (install with `sudo apt install curl` if needed)

## Step 1: Add the Repository

First, we need to add the GPG key and repository to your system. Run the following commands in your terminal:

```bash
# Add the GPG key
curl -sS https://debian.griffo.io/EA0F721D231FDD3A0A17B9AC7808B4DD62C41256.asc | sudo gpg --dearmor --yes -o /etc/apt/trusted.gpg.d/debian.griffo.io.gpg

# Add the repository
echo "deb https://debian.griffo.io/apt $(lsb_release -sc 2>/dev/null) main" | sudo tee /etc/apt/sources.list.d/debian.griffo.io.list
```

Let's break down what these commands do:

1. **GPG Key Addition**: Downloads and installs the repository's GPG key, which ensures the authenticity of the packages
2. **Repository Addition**: Adds the repository URL to your APT sources list, automatically detecting your Debian distribution

## Step 2: Update Package Lists

After adding the repository, update your package lists:

```bash
sudo apt update
```

## Step 3: Install Zig

Now you can install Zig using APT. The repository provides two options:

### Install Stable Zig

For the stable release of Zig:

```bash
sudo apt install zig
```

### Install Nightly Zig

For the latest nightly build (cutting-edge features):

```bash
sudo apt install zig-master
```

## Step 4: Verify Installation

After installation, verify that Zig is working correctly:

```bash
zig version
```

You should see output similar to:

```
0.13.0
```

Or for nightly builds:

```
0.14.0-dev.1911+3bf89f55c
```

## Bonus: Installing ZLS (Zig Language Server)

The repository also provides ZLS, the Language Server Protocol implementation for Zig, which enables IDE features in your editor:

### Stable ZLS

```bash
sudo apt install zls
```

### Nightly ZLS

```bash
sudo apt install zls-master
```

## Updating Zig

One of the biggest advantages of using the repository is easy updates. To update Zig (and all other packages):

```bash
sudo apt update && sudo apt upgrade
```

## Other Available Packages

The `debian.griffo.io` repository doesn't just provide Zig. It also includes many other useful development tools:

- **lazydocker**: Terminal UI for Docker
- **lazygit**: Terminal UI for Git
- **yazi**: Terminal file manager written in Rust
- **fzf**: Command-line fuzzy finder
- **uv**: Fast Python package manager
- **ghostty**: Fast terminal emulator
- And many more!

## Troubleshooting

### Repository Key Issues

If you encounter GPG key issues, make sure you're using the correct key. The repository updated its public key on March 4th, 2025, so older installations might need to update:

```bash
# Remove old key if it exists
sudo rm -f /etc/apt/trusted.gpg.d/debian.griffo.io.gpg

# Add the new key
curl -sS https://debian.griffo.io/EA0F721D231FDD3A0A17B9AC7808B4DD62C41256.asc | sudo gpg --dearmor --yes -o /etc/apt/trusted.gpg.d/debian.griffo.io.gpg
```

### Package Not Found

If APT can't find the Zig package:

1. Ensure you've run `sudo apt update` after adding the repository
2. Check that your Debian distribution is supported (Bookworm, Trixie, or Sid)
3. Verify the repository was added correctly: `cat /etc/apt/sources.list.d/debian.griffo.io.list`

## Uninstalling

If you need to remove Zig:

```bash
# Remove Zig
sudo apt remove zig

# Or remove nightly Zig
sudo apt remove zig-master

# Remove the repository (optional)
sudo rm /etc/apt/sources.list.d/debian.griffo.io.list
sudo rm /etc/apt/trusted.gpg.d/debian.griffo.io.gpg
sudo apt update
```

## Building Your First Zig Program

Now that Zig is installed, let's create a simple "Hello, World!" program:

```bash
# Create a new file
echo 'const std = @import("std");

pub fn main() void {
    std.debug.print("Hello, World!\\n", .{});
}' > hello.zig

# Compile and run
zig run hello.zig
```

You should see:

```
Hello, World!
```

## Conclusion

Installing Zig on Debian doesn't have to be complicated. The `debian.griffo.io` repository provides an easy, maintainable way to install and keep Zig up-to-date on your Debian system. Whether you need the stable release for production work or the nightly builds for experimenting with the latest features, this repository has you covered.

The repository is maintained as an unofficial community project and provides packages that aren't available in the official Debian repositories. It's a valuable resource for Debian users who want access to modern development tools without the hassle of manual compilation and installation.

Happy coding with Zig!

## Resources

- [Zig Official Website](https://ziglang.org/)
- [debian.griffo.io Repository](https://debian.griffo.io/)
- [Zig Documentation](https://ziglang.org/documentation/)
- [Zig GitHub Repository](https://github.com/ziglang/zig)
- [ZLS GitHub Repository](https://github.com/zigtools/zls)

---

*Disclaimer: The debian.griffo.io repository is an unofficial community project and is not affiliated with the official Debian project or the Zig programming language team.*
