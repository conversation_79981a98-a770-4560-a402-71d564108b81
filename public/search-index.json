[{"title": "Building a Production-Ready MCP Server in .NET - A PostgreSQL Memory Example", "description": "Learn how to implement a practical Model Context Protocol (MCP) server in .NET using PostgreSQL with pgvector for AI memory storage, featuring a complete working example that goes beyond simple demos.", "url": "/posts/building-mcp-server-dotnet-postgresql-memory/", "content": "# Building a Production-Ready MCP Server in .NET: A PostgreSQL Memory Example\n\nIn the rapidly evolving world of AI integration, enabling Large Language Models (LLMs) to interact with external systems is becoming increasingly important. The Model Context Protocol (MCP) has emerged as a standardized way to connect AI models with various tools and data sources, making it easier to build powerful AI-enabled applications.\n\nToday, we'll explore how to implement a production-ready MCP server in .NET by examining a real-world example: the [postg-mem](https://github.com/dariogriffo/postg-mem) project. This MCP server provides vector-based memory storage for AI applications using PostgreSQL with the pgvector extension, demonstrating how to go beyond simple \"Hello World\" examples to create a practical, useful service.\n\n## Understanding MCP and Why It Matters\n\nThe Model Context Protocol (MCP) is an open protocol that standardizes how applications provide context to LLMs. It acts like a \"USB-C port for AI applications,\" providing a universal way to connect AI models to different data sources and tools.\n\nAt its core, MCP follows a client-server architecture:\n\n- **MCP Hosts**: Applications like Claude Desktop, VS Code, or AI-powered tools that want to access data through MCP\n- **MCP Clients**: Protocol clients that maintain connections with servers\n- **MCP Servers**: Lightweight programs that expose specific capabilities through the standardized protocol\n- **Data Sources**: Your computer's files, databases, or external services that the MCP servers can access\n\nThis architecture allows LLMs to interact with various systems through a consistent interface, making it easier to build complex AI applications without needing to implement custom integration code for each new tool or data source.\n\n## Why Vector Memory Matters for AI Applications\n\nOne of the key challenges in building AI applications is providing them with memory. LLMs by themselves are stateless—they don't remember previous interactions beyond what's included in the current prompt. To build AI agents that can maintain context over time, we need a way to store and retrieve information efficiently.\n\nVector databases are particularly well-suited for this task because they allow for semantic search—finding information based on meaning rather than just keywords. When we store text as vector embeddings (numerical representations that capture semantic meaning), we can find similar content even if it doesn't share the same exact words.\n\nPostgreSQL with the pgvector extension gives us the best of both worlds:\n\n1. A robust, mature relational database for structured data\n2. Vector similarity search capabilities for semantic retrieval\n3. The ability to combine traditional queries with vector search\n\nThis combination makes it an excellent choice for implementing AI memory systems that need to store, organize, and retrieve information efficiently.\n\n## The PostgMem Project: An Overview\n\nThe [postg-mem](https://github.com/dariogriffo/postg-mem) project implements an MCP server that provides vector memory storage using PostgreSQL and pgvector. It allows AI agents to:\n\n- Store structured memories with vector embeddings\n- Retrieve memories by ID\n- Perform semantic search through memories using vector similarity\n- Filter search results using tags\n\nIt's built using .NET 9.0 and integrates with the MCP C# SDK, making it a great example of how to implement MCP servers in the .NET ecosystem.\n\n## Setting Up the Foundation\n\nThe PostgMem project uses a standard ASP.NET Core application structure. Let's start by examining the key components needed to set up an MCP server in .NET.\n\n### Project Setup\n\nThe project is configured as an ASP.NET Core web application with the following NuGet packages:\n\n```xml\n<ItemGroup>\n    <PackageReference Include=\"Configuration.Extensions.EnvironmentFile\" Version=\"2.0.0\" />\n    <PackageReference Include=\"Microsoft.Extensions.Configuration.EnvironmentVariables\" Version=\"9.0.3\" />\n    <PackageReference Include=\"Microsoft.Extensions.Hosting\" Version=\"9.0.3\" />\n    <PackageReference Include=\"Microsoft.Extensions.Http\" Version=\"9.0.3\" />\n    <PackageReference Include=\"ModelContextProtocol\" Version=\"0.1.0-preview.5\" />\n    <PackageReference Include=\"ModelContextProtocol.AspNetCore\" Version=\"0.1.0-preview.5\" />\n    <PackageReference Include=\"Npgsql\" Version=\"9.0.3\" />\n    <PackageReference Include=\"pgvector\" Version=\"0.3.1\" />\n    <PackageReference Include=\"Registrator.Net\" Version=\"3.0.0\" />\n    <PackageReference Include=\"Serilog.Extensions.Logging.File\" Version=\"3.0.0\" />\n</ItemGroup>\n```\n\nThe two essential packages for MCP functionality are `ModelContextProtocol` and `ModelContextProtocol.AspNetCore`, which provide the core SDK and ASP.NET Core integration, respectively.\n\n### Application Startup\n\nThe `Program.cs` file sets up the MCP server along with the necessary services:\n\n```csharp\nWebApplicationBuilder builder = WebApplication.CreateBuilder(args);\n\nbuilder\n    .Configuration\n    .AddEnvironmentFile() \n    .AddEnvironmentVariables(\"POSTGMEM_\"); \n\nbuilder\n    .Logging\n    .AddConsole(options => { options.LogToStandardErrorThreshold = LogLevel.Trace; })\n    .AddFile(\"log.log\", minimumLevel: LogLevel.Trace);\n\nbuilder\n    .Services\n    .AddPostgMem()\n    .AddMcpServer().WithTools<MemoryTools>();\n\nWebApplication app = builder.Build();\n\napp.MapMcp();\napp.Run();\n```\n\nLet's break down the key points:\n\n1. The application uses the `Configuration.Extensions.EnvironmentFile` package to load configuration from a `.env` file, which is a convenient way to manage environment-specific settings.\n\n2. It sets up logging to both the console and a file, ensuring that we have proper observability in production.\n\n3. It registers the PostgMem services using a custom extension method `AddPostgMem()`, which we'll examine shortly.\n\n4. It adds the MCP server with the `AddMcpServer()` method and registers our memory tools using `WithTools<MemoryTools>()`.\n\n5. Finally, it maps the MCP endpoints with `app.MapMcp()`, which sets up the necessary routes for the MCP protocol.\n\n### Service Registration Extensions\n\nThe `AddPostgMem()` extension method is defined in the `ServiceCollectionExtensions.cs` file and handles registering the various services needed for the application:\n\n```csharp\ninternal static class ServiceCollectionExtensions\n{\n    internal static IServiceCollection AddPostgMem(\n        this IServiceCollection services)\n    {\n        services.AddEmbeddings();\n        services.AddStorage();\n        return services;\n    }\n\n    internal static IServiceCollection AddEmbeddings(\n        this IServiceCollection services)\n    {\n        services\n            .AddSingleton<EmbeddingSettings>(sp =>\n                sp.GetRequiredService<IConfiguration>().GetSection(\"Embeddings\").Get<EmbeddingSettings>() ??\n                throw new ArgumentNullException(\"Embeddings Settings\"))\n            .AddHttpClient<IEmbeddingService, EmbeddingService>((sp, client) =>\n            {\n                EmbeddingSettings settings = sp.GetRequiredService<EmbeddingSettings>();\n                client.BaseAddress = settings.ApiUrl;\n                client.Timeout = settings.Timeout;\n            });\n\n        return services;\n    }\n\n    internal static IServiceCollection AddStorage(\n        this IServiceCollection services)\n    {\n        services\n            .AddSingleton(sp =>\n            {\n                string connectionString =\n                    sp.GetRequiredService<IConfiguration>().GetConnectionString(\"Storage\") ??\n                    throw new ArgumentNullException(\"Storage Connection String\");\n                NpgsqlDataSourceBuilder sourceBuilder = new(connectionString);\n                sourceBuilder.UseVector();\n                return sourceBuilder.Build();\n            });\n        return services;\n    }\n}\n```\n\nThis extension method registers two main services:\n\n1. The embedding service, which is responsible for generating vector embeddings from text.\n2. The storage service, which handles database operations using Npgsql with the pgvector extension.\n\nNote the use of `sourceBuilder.UseVector()`, which enables the pgvector extension for Npgsql, allowing us to work with vector types in .NET.\n\n## The Memory Model and Storage\n\nAt the core of the PostgMem project is the memory model, which defines the structure of the data we'll be storing:\n\n```csharp\npublic class Memory\n{\n    public Guid Id { get; init; }\n    public string Type { get; init; } = string.Empty;\n    public JsonDocument Content { get; init; } = JsonDocument.Parse(\"{}\");\n    public string Source { get; init; } = string.Empty;\n    public Vector Embedding { get; init; } = new(new float[384]);\n    public string[]? Tags { get; init; }\n    public double Confidence { get; init; }\n    public DateTime CreatedAt { get; init; }\n    public DateTime UpdatedAt { get; init; }\n}\n```\n\nThis model includes:\n\n- A unique identifier\n- Type and source fields for categorization\n- JSON content stored as a `JsonDocument`\n- A vector embedding for semantic search\n- Tags for filtering\n- A confidence score\n- Creation and update timestamps\n\nThe `Vector` type comes from the pgvector package and represents a vector embedding in PostgreSQL.\n\nThe storage service implements the `IStorage` interface:\n\n```csharp\npublic interface IStorage\n{\n    Task<Memory> StoreMemory(\n        string type,\n        string content,\n        string source,\n        string[]? tags,\n        double confidence,\n        CancellationToken cancellationToken = default\n    );\n\n    Task<List<Memory>> Search(\n        string query,\n        int limit = 10,\n        double minSimilarity = 0.7,\n        string[]? filterTags = null,\n        CancellationToken cancellationToken = default\n    );\n\n    Task<Memory?> Get(\n        Guid id,\n        CancellationToken cancellationToken = default\n    );\n\n    Task<bool> Delete(\n        Guid id,\n        CancellationToken cancellationToken = default\n    );\n}\n```\n\nThis interface defines the core operations for our memory system:\n\n- `StoreMemory`: Stores a new memory with the provided metadata and content\n- `Search`: Performs a semantic search for memories similar to a query\n- `Get`: Retrieves a specific memory by ID\n- `Delete`: Removes a memory from the database\n\nLet's look at how the `Search` method is implemented in the `Storage` class:\n\n```csharp\npublic async Task<List<Memory>> Search(\n    string query,\n    int limit = 10,\n    double minSimilarity = 0.7,\n    string[]? filterTags = null,\n    CancellationToken cancellationToken = default\n)\n{\n    // Generate embedding for the query\n    float[] queryEmbedding = await _embeddingService.Generate(\n        query,\n        cancellationToken\n    );\n    \n    await using NpgsqlConnection connection = await _dataSource.OpenConnectionAsync(cancellationToken);\n\n    string sql =\n        @\"\n        SELECT id, type, content, source, embedding, tags, confidence, created_at, updated_at\n        FROM memories\n        WHERE embedding <=> @embedding < @maxDistance\";\n\n    if (filterTags is { Length: > 0 })\n    {\n        sql += \" AND tags @> @tags\";\n    }\n\n    sql += \" ORDER BY embedding <=> @embedding LIMIT @limit\";\n\n    await using NpgsqlCommand cmd = new(sql, connection);\n    cmd.Parameters.AddWithValue(\"embedding\", new Vector(queryEmbedding));\n    cmd.Parameters.AddWithValue(\"maxDistance\", 1 - minSimilarity); \n    cmd.Parameters.AddWithValue(\"limit\", limit);\n\n    if (filterTags != null && filterTags.Length > 0)\n    {\n        cmd.Parameters.AddWithValue(\"tags\", filterTags);\n    }\n\n    List<Memory> memories = [];\n    await using NpgsqlDataReader reader = await cmd.ExecuteReaderAsync(cancellationToken);\n\n    while (await reader.ReadAsync(cancellationToken))\n    {\n        memories.Add(\n            new Memory\n            {\n                Id = reader.GetGuid(0),\n                Type = reader.GetString(1),\n                Content = reader.GetFieldValue<JsonDocument>(2),\n                Source = reader.GetString(3),\n                Embedding = reader.GetFieldValue<Vector>(4),\n                Tags = reader.GetFieldValue<string[]>(5),\n                Confidence = reader.GetDouble(6),\n                CreatedAt = reader.GetDateTime(7),\n                UpdatedAt = reader.GetDateTime(8),\n            }\n        );\n    }\n\n    return memories;\n}\n```\n\nThis method:\n\n1. Generates a vector embedding for the search query using the embedding service\n2. Constructs a SQL query that uses pgvector's cosine distance operator (`<=>`) to find similar memories\n3. Adds a clause for filtering by tags if specified\n4. Orders the results by similarity and limits the number of results\n5. Maps the database records to `Memory` objects and returns them\n\nThe `<=>` operator calculates the cosine distance between vectors, which is 1 minus the cosine similarity. A lower distance means higher similarity, so we're finding memories with the closest embeddings to our query.\n\n## Embedding Generation\n\nTo perform semantic search, we need to convert text into vector embeddings. The `EmbeddingService` handles this task:\n\n```csharp\npublic async Task<float[]> Generate(\n    string text,\n    CancellationToken cancellationToken = default\n)\n{\n    try\n    {\n        _logger.LogDebug(\"Generating embedding for text of length {TextLength}\", text.Length);\n\n        EmbeddingRequest request = new() { Model = _settings.Model, Prompt = text };\n\n        _logger.LogDebug(\"Sending request to embedding API at {ApiUrl}\", _settings.ApiUrl);\n        HttpResponseMessage response = await _httpClient.PostAsJsonAsync(\n            \"api/embeddings\",\n            request,\n            cancellationToken\n        );\n        response.EnsureSuccessStatusCode();\n\n        EmbeddingResponse? result =\n            await response.Content.ReadFromJsonAsync<EmbeddingResponse>(\n                cancellationToken: cancellationToken\n            );\n        if (result?.Embedding == null || result.Embedding.Length == 0)\n        {\n            throw new Exception(\"Failed to generate embedding: Empty response from API\");\n        }\n\n        _logger.LogDebug(\"Successfully generated embedding with {Dimensions} dimensions\", result.Embedding.Length);\n\n        return result.Embedding;\n    }\n    catch (Exception ex)\n    {\n        _logger.LogError(ex, \"Error generating embedding: {ErrorMessage}\", ex.Message);\n\n        // Fallback to a random embedding in case of error\n        _logger.LogWarning(\"Falling back to random embedding generation\");\n        Random random = new();\n        float[] embedding = new float[384];\n        for (int i = 0; i < embedding.Length; i++)\n        {\n            embedding[i] = (float)random.NextDouble();\n        }\n\n        // Normalize the embedding\n        float sum = 0;\n        for (int i = 0; i < embedding.Length; i++)\n        {\n            sum += embedding[i] * embedding[i];\n        }\n\n        float magnitude = (float)Math.Sqrt(sum);\n        for (int i = 0; i < embedding.Length; i++)\n        {\n            embedding[i] /= magnitude;\n        }\n\n        return embedding;\n    }\n}\n```\n\nThis service:\n\n1. Sends the text to an external embeddings API (by default, it uses Ollama)\n2. Parses the response to extract the vector embedding\n3. Includes a fallback mechanism that generates a random embedding if the API call fails\n4. Normalizes the fallback embedding to ensure it has unit length\n\nThe fallback mechanism is an important production consideration—it ensures that the application can continue to function even if the embedding API is temporarily unavailable, though with reduced effectiveness.\n\n## Implementing MCP Tools\n\nNow that we have the core services in place, let's see how we expose them through MCP. The `MemoryTools` class implements the MCP tools that allow AI agents to interact with our memory system:\n\n```csharp\n[McpServerToolType]\npublic class MemoryTools\n{\n    private readonly IStorage _storage;\n\n    public MemoryTools(IStorage storage)\n    {\n        _storage = storage;\n    }\n\n    [McpServerTool, Description(\"Store a new memory in the database\")]\n    public async Task<string> Store(\n        [Description(\"The type of memory (e.g., 'conversation', 'document', etc.)\")] string type,\n        [Description(\"The content of the memory as a JSON object\")] string content,\n        [Description(\"The source of the memory (e.g., 'user', 'system', etc.)\")] string source,\n        [Description(\"Optional tags to categorize the memory\")] string[]? tags = null,\n        [Description(\"Confidence score for the memory (0.0 to 1.0)\")] double confidence = 1.0,\n        CancellationToken cancellationToken = default\n    )\n    {\n        // Store the memory\n        Memory memory = await _storage.StoreMemory(\n            type,\n            content,\n            source,\n            tags,\n            confidence,\n            cancellationToken\n        );\n\n        return $\"Memory stored successfully with ID: {memory.Id}\";\n    }\n\n    [McpServerTool, Description(\"Search for memories similar to the provided text\")]\n    public async Task<string> Search(\n        [Description(\"The text to search for similar memories\")] string query,\n        [Description(\"Maximum number of results to return\")] int limit = 10,\n        [Description(\"Minimum similarity threshold (0.0 to 1.0)\")] double minSimilarity = 0.7,\n        [Description(\"Optional tags to filter memories\")] string[]? filterTags = null,\n        CancellationToken cancellationToken = default\n    )\n    {\n        // Search for similar memories\n        List<Memory> memories = await _storage.Search(\n            query,\n            limit,\n            minSimilarity,\n            filterTags,\n            cancellationToken\n        );\n\n        if (memories.Count == 0)\n        {\n            return \"No memories found matching your query.\";\n        }\n\n        // Format the results\n        StringBuilder result = new();\n        result.AppendLine($\"Found {memories.Count} memories:\");\n        result.AppendLine();\n\n        foreach (Memory? memory in memories)\n        {\n            result.AppendLine($\"ID: {memory.Id}\");\n            result.AppendLine($\"Type: {memory.Type}\");\n            result.AppendLine($\"Content: {memory.Content.RootElement}\");\n            result.AppendLine($\"Source: {memory.Source}\");\n            result.AppendLine(\n                $\"Tags: {(memory.Tags != null ? string.Join(\", \", memory.Tags) : \"none\")}\"\n            );\n            result.AppendLine($\"Confidence: {memory.Confidence:F2}\");\n            result.AppendLine($\"Created: {memory.CreatedAt:yyyy-MM-dd HH:mm:ss}\");\n            result.AppendLine();\n        }\n\n        return result.ToString();\n    }\n\n    [McpServerTool, Description(\"Retrieve a specific memory by ID\")]\n    public async Task<string> Get(\n        [Description(\"The ID of the memory to retrieve\")] Guid id,\n        CancellationToken cancellationToken = default\n    )\n    {\n        Memory? memory = await _storage.Get(id, cancellationToken);\n\n        if (memory == null)\n        {\n            return $\"Memory with ID {id} not found.\";\n        }\n\n        StringBuilder result = new();\n        result.AppendLine($\"ID: {memory.Id}\");\n        result.AppendLine($\"Type: {memory.Type}\");\n        result.AppendLine($\"Content: {memory.Content.RootElement}\");\n        result.AppendLine($\"Source: {memory.Source}\");\n        result.AppendLine(\n            $\"Tags: {(memory.Tags != null ? string.Join(\", \", memory.Tags) : \"none\")}\"\n        );\n        result.AppendLine($\"Confidence: {memory.Confidence:F2}\");\n        result.AppendLine($\"Created: {memory.CreatedAt:yyyy-MM-dd HH:mm:ss}\");\n        result.AppendLine($\"Updated: {memory.UpdatedAt:yyyy-MM-dd HH:mm:ss}\");\n\n        return result.ToString();\n    }\n\n    [McpServerTool, Description(\"Delete a memory by ID\")]\n    public async Task<string> Delete(\n        [Description(\"The ID of the memory to delete\")] Guid id,\n        CancellationToken cancellationToken = default\n    )\n    {\n        bool success = await _storage.Delete(id, cancellationToken);\n\n        return success \n            ? $\"Memory with ID {id} deleted successfully.\" \n            : $\"Memory with ID {id} not found or could not be deleted.\";\n    }\n}\n```\n\nThe key elements here are:\n\n1. The `McpServerToolType` attribute on the class, which marks it as a container for MCP tools.\n2. The `McpServerTool` attribute on each method, which exposes it as an MCP tool.\n3. The `Description` attributes on methods and parameters, which provide information to the AI about what each tool and parameter does.\n4. The return type of `string` for all methods, which provides formatted responses that the AI can interpret.\n\nThese tools map directly to the operations defined in our `IStorage` interface, providing a clean, well-documented API for AI agents to interact with our memory system.\n\n## Running and Testing\n\nTo run the PostgMem MCP server, you'll need:\n\n1. .NET 9.0 SDK\n2. PostgreSQL with the pgvector extension installed\n3. An embedding API (the default configuration uses Ollama)\n\nFirst, set up the PostgreSQL database:\n\n```sql\nCREATE EXTENSION vector;\n\nCREATE TABLE memories (\n    id UUID PRIMARY KEY,\n    type TEXT NOT NULL,\n    content JSONB NOT NULL,\n    source TEXT NOT NULL,\n    embedding VECTOR(384) NOT NULL,\n    tags TEXT[] NOT NULL,\n    confidence DOUBLE PRECISION NOT NULL,\n    created_at TIMESTAMP WITH TIME ZONE NOT NULL,\n    updated_at TIMESTAMP WITH TIME ZONE NOT NULL\n);\n```\n\nThen configure the application by creating a `.env` file:\n\n```\nConnectionStrings__Storage=\"Host=localhost;Database=mcp_memory;Username=postgres;Password=*********"\nEmbeddings__ApiUrl=http://localhost:11434/\nEmbeddings__Model=all-minilm:33m-l12-v2-fp16\n```\n\nFinally, run the application:\n\n```bash\ndotnet run\n```\n\nThe MCP server will be available at `http://localhost:5000` by default, ready to accept connections from MCP clients.\n\n## Using the MCP Server with AI Agents\n\nOnce the server is running, you can use it with any MCP-compatible client, such as GitHub Copilot in VS Code or Claude Desktop. Here's an example of how you might configure it in a `mcp.json` file for VS Code:\n\n```json\n{\n    \"servers\": {\n        \"postgmem\": {\n            \"type\": \"stdio\",\n            \"command\": \"dotnet\",\n            \"args\": [\n                \"run\",\n                \"--project\",\n                \"/path/to/postg-mem/PostgMem/PostgMem.csproj\"\n            ]\n        }\n    }\n}\n```\n\nWith this configuration, the AI agent can interact with the memory system through natural language. For example:\n\n- \"Store this fact about PostgreSQL: { 'fact': 'PostgreSQL supports vector search through the pgvector extension' }\"\n- \"What do you know about PostgreSQL vector search?\"\n- \"Retrieve the memory with ID 12345678-9abc-def0-1234-56789abcdef0\"\n- \"Delete the memory about PostgreSQL vector search\"\n\nThe MCP server handles these requests, executes the appropriate operations, and returns the results to the AI agent, which can then incorporate them into its responses.\n\n## Production Considerations\n\nWhile the PostgMem project provides a solid foundation for an MCP server, there are several considerations to keep in mind for production deployments:\n\n### Security\n\nThe example doesn't include authentication or authorization mechanisms. In a production environment, you would want to:\n\n1. Implement authentication for MCP clients\n2. Add authorization rules to control which clients can access which tools\n3. Validate and sanitize inputs to prevent injection attacks\n4. Use HTTPS for all communications\n\n### Scaling\n\nFor high-traffic applications, consider:\n\n1. Using connection pooling for database access\n2. Setting up database replication for read-heavy workloads\n3. Implementing caching for frequently accessed memories\n4. Using a more scalable embedding service\n\n### Monitoring and Logging\n\nThe example includes basic logging, but a production system would benefit from:\n\n1. Structured logging with correlation IDs\n2. Metrics collection for key operations\n3. Alerts for error conditions\n4. Distributed tracing for complex request flows\n\n### Error Handling\n\nWhile the embedding service includes fallback handling, a comprehensive approach would:\n\n1. Implement circuit breakers for external dependencies\n2. Add retry mechanisms with exponential backoff\n3. Provide graceful degradation paths for all failure modes\n4. Maintain detailed error logs for troubleshooting\n\n### Deployment\n\nFor deploying the MCP server:\n\n1. Containerize the application using Docker\n2. Use Kubernetes for orchestration\n3. Set up CI/CD pipelines for automated deployments\n4. Implement blue/green deployment strategies for zero-downtime updates\n\n## Beyond Memory: Other Applications\n\nThe PostgMem example focuses on memory storage, but the MCP server pattern can be applied to many other use cases:\n\n1. **Document Management**: Implement tools for storing, retrieving, and searching documents\n2. **Data Analysis**: Create tools for performing statistical analysis on datasets\n3. **External API Integration**: Build tools that interact with third-party services\n4. **Process Automation**: Develop tools that execute workflows in response to AI requests\n5. **Content Generation**: Create tools that generate or transform content based on AI inputs\n\nBy following the patterns established in the PostgMem project, you can build MCP servers for any of these applications, providing AI agents with powerful capabilities beyond what they can do on their own.\n\n## Conclusion\n\nThe PostgMem project demonstrates how to build a production-ready MCP server in .NET using PostgreSQL with pgvector for AI memory storage. By following this example, you can create your own MCP servers that provide AI agents with access to specialized tools and data sources.\n\nKey takeaways from this exploration:\n\n1. MCP provides a standardized way for AI models to interact with external systems, enabling more powerful and flexible AI applications.\n2. .NET offers a robust platform for implementing MCP servers, with strong typing, excellent performance, and a rich ecosystem of libraries.\n3. PostgreSQL with pgvector provides an excellent foundation for AI memory systems, combining traditional database capabilities with vector similarity search.\n4. Building production-ready MCP servers requires careful attention to architecture, error handling, security, and performance considerations.\n\nAs AI continues to evolve, the ability to extend its capabilities through tools like MCP servers will become increasingly important. By mastering these techniques, you can build AI applications that combine the natural language understanding of LLMs with the specific functionality and data access that your use cases require.\n\n## Resources\n\n- [PostgMem GitHub Repository](https://github.com/dariogriffo/postg-mem)\n- [Model Context Protocol](https://modelcontextprotocol.io/introduction)\n- [MCP C# SDK](https://github.com/modelcontextprotocol/csharp-sdk)\n- [pgvector Extension](https://github.com/pgvector/pgvector)\n- [Configuration.Extensions.EnvironmentFile](https://www.nuget.org/packages/Configuration.Extensions.EnvironmentFile/)\n- [.NET 9.0 Documentation](https://docs.microsoft.com/en-us/dotnet/)"}, {"title": "Building Custom Configuration Providers in .NET", "description": "Learn how to extend .NET's configuration system by creating custom configuration providers, with a practical example using the Environment File Provider.", "url": "/posts/custom-configuration-providers-dotnet/", "content": "# Building Custom Configuration Providers in .NET\n\nOne of the most powerful features of the .NET configuration system is its extensibility. While .NET provides many built-in configuration providers for common scenarios like JSON files, environment variables, and command-line arguments, you might encounter situations where you need a custom configuration source.\n\nIn this post, we'll explore how to create custom configuration providers by examining a real-world example: the [Configuration.Extensions.EnvironmentFile](https://github.com/dariogriffo/Configuration.Extensions.EnvironmentFile) package, which allows .NET applications to use Unix-style environment files (`.env`) for configuration.\n\n## Why Custom Configuration Providers?\n\nBefore diving into implementation details, let's consider some scenarios where custom configuration providers are useful:\n\n- Supporting configuration formats not covered by built-in providers\n- Retrieving configuration from specialized services or databases\n- Implementing specific validation or transformation logic for configuration values\n- Adding environment-specific configuration handling\n\nThe EnvironmentFile provider addresses the first scenario by supporting `.env` files, a popular format in the Unix world for configuring services.\n\n## Understanding the .NET Configuration Architecture\n\nThe .NET configuration system follows a provider pattern with these key components:\n\n1. **Configuration Sources**: Define where configuration data comes from\n2. **Configuration Providers**: Read from sources and provide data to the configuration system\n3. **Configuration Builder**: Combines multiple providers into a unified configuration\n4. **Configuration Root**: The final configuration that applications consume\n\nEach provider implements specific interfaces from the `Microsoft.Extensions.Configuration` namespace. Let's look at what we need to build a custom configuration provider.\n\n## Implementing a Custom Configuration Provider\n\nUsing the [Configuration.Extensions.EnvironmentFile](https://github.com/dariogriffo/Configuration.Extensions.EnvironmentFile) package as our example, we'll walk through the essential components needed to create a custom configuration provider.\n\n### 1. Define the Configuration Source\n\nThe configuration source defines where and how to get configuration data. It implements `IConfigurationSource` and creates the actual provider.\n\n```csharp\npublic class EnvironmentFileConfigurationSource : FileConfigurationSource\n{\n    public EnvironmentFileConfigurationSource()\n    {\n        Path = \".env\";\n    }\n\n    internal string? Prefix { get; set; }\n    internal bool RemoveWrappingQuotes { get; set; } = true;\n    internal bool Trim { get; set; } = true;\n\n    public override IConfigurationProvider Build(IConfigurationBuilder builder)\n    {\n        EnsureDefaults(builder);\n        return new EnvironmentFileConfigurationProvider(this, Trim, RemoveWrappingQuotes, Prefix);\n    }\n}\n```\n\nThis source inherits from `FileConfigurationSource` because it works with files, which gives us file path handling and file provider functionality for free. The source defines specific options like prefix handling and quote formatting, then creates a provider instance in the `Build` method.\n\n### 2. Create the Configuration Provider\n\nThe provider does the actual work of reading and parsing the configuration data:\n\n```csharp\ninternal class EnvironmentFileConfigurationProvider : FileConfigurationProvider\n{\n    private readonly bool _trim;\n    private readonly bool _removeWrappingQuotes;\n    private readonly string? _prefix;\n\n    public override void Load(Stream stream)\n    {\n        Data = EnvironmentFileConfigurationParser.Parse(\n            stream,\n            _trim,\n            _removeWrappingQuotes,\n            _prefix\n        );\n    }\n\n    internal EnvironmentFileConfigurationProvider(\n        FileConfigurationSource source,\n        bool trim,\n        bool removeWrappingQuotes,\n        string? prefix\n    )\n        : base(source)\n    {\n        _trim = trim;\n        _removeWrappingQuotes = removeWrappingQuotes;\n        _prefix = prefix;\n    }\n}\n```\n\nBy inheriting from `FileConfigurationProvider`, we get file loading functionality automatically. We just need to override the `Load` method to parse the file content. In this case, the provider delegates the parsing to a separate class.\n\n### 3. Implement the Parser\n\nThe parser contains the logic for extracting key-value pairs from the configuration source:\n\n```csharp\ninternal static class EnvironmentFileConfigurationParser\n{\n    public static Dictionary<string, string> Parse(\n        Stream stream,\n        bool trim,\n        bool removeWrappingQuotes,\n        string? prefix\n    )\n    {\n        StreamReader reader = new(stream);\n\n        IEnumerable<string> nonCommentLinesWithPropertyValues = reader\n            .ReadToEnd()\n            .Split([\"\\r\\n\", \"\\r\", \"\\n\"], StringSplitOptions.None)\n            .Select(x => x.TrimStart())\n            .Select(x => string.IsNullOrWhiteSpace(prefix) ? x : x.Replace(prefix, string.Empty))\n            .Where(x => !x.StartsWith(\"#\") && x.Contains(\"=\"));\n\n        IEnumerable<KeyValuePair<string, string>>? configuration = nonCommentLinesWithPropertyValues\n            .Select(ParseQuotes)\n            .Select(x => RemoveCommentsAtTheEndAndTrimIfNecessary(x, trim))\n            .Select(x => x.Split('='))\n            .Select(x => new KeyValuePair<string, string>(\n                x[0].Replace(\"__\", \":\"),\n                string.Join(\"=\", x.Skip(1))\n            ));\n\n        return configuration.ToDictionary(x => x.Key, x => x.Value);\n\n        // Helper methods for processing values\n        string ParseQuotes(string line)\n        {\n            if (!removeWrappingQuotes)\n            {\n                return line;\n            }\n\n            string[] parts = line.Split('=');\n            line = string.Join(\"=\", parts.Skip(1));\n            return $\"{parts[0]}={line.Trim('\"')}\";\n        }\n\n        static string RemoveCommentsAtTheEndAndTrimIfNecessary(string line, bool t)\n        {\n            return t ? line.Trim() : line;\n        }\n    }\n}\n```\n\nThis parser handles:\n- Reading the file line by line\n- Filtering out comment lines and empty lines\n- Handling prefixes by removing them if specified\n- Processing equals signs in values (preserving them after the first one)\n- Converting double underscores to colons for hierarchical configuration\n- Optionally trimming whitespace and removing quotes\n\n### 4. Provide Extensions Methods for Easy Registration\n\nTo make the provider easy to use, we need extension methods for the `IConfigurationBuilder`:\n\n```csharp\npublic static class EnvironmentFileConfigurationExtensions\n{\n    public static IConfigurationBuilder AddEnvironmentFile(\n        this IConfigurationBuilder builder,\n        string fileName = \".env\",\n        bool trim = true,\n        bool removeWrappingQuotes = true,\n        string? prefix = null,\n        bool reloadOnChange = false\n    )\n    {\n        string? directory;\n        if (!Path.IsPathRooted(fileName))\n        {\n            directory =\n                builder.Properties.TryGetValue(\"FileProvider\", out object? p)\n                && p is FileConfigurationProvider configurationProvider\n                    ? Path.GetDirectoryName(configurationProvider.Source.Path)\n                    : Directory.GetCurrentDirectory();\n        }\n        else\n        {\n            directory = EnsureTrailingSlash(Path.GetFullPath(fileName));\n        }\n\n        Action<EnvironmentFileConfigurationSource> configureSource = s =>\n        {\n            s.Prefix = prefix;\n            s.RemoveWrappingQuotes = removeWrappingQuotes;\n            s.Trim = trim;\n            s.Path = fileName;\n            s.Optional = true;\n            s.ReloadOnChange = reloadOnChange;\n            s.FileProvider = new PhysicalFileProvider(directory!, ExclusionFilters.None);\n        };\n        return builder.Add(configureSource);\n    }\n\n    private static string EnsureTrailingSlash(string path)\n    {\n        return !string.IsNullOrEmpty(path) && path[path.Length - 1] != Path.DirectorySeparatorChar\n            ? path + Path.DirectorySeparatorChar\n            : path;\n    }\n}\n```\n\nThese extensions make it easy to add our provider to the configuration pipeline with sensible defaults and customization options.\n\n## Integrating the Custom Provider\n\nWith all components in place, users can easily add this provider to their applications:\n\n```csharp\npublic static IHostBuilder CreateHostBuilder(string[] args) =>\n    Host.CreateDefaultBuilder(args)\n        .ConfigureAppConfiguration((hostingContext, config) =>\n        {\n            config\n                .AddEnvironmentFile() // Use default .env file\n                .AddEnvironmentFile(\"database.env\") // Add another env file\n                .AddEnvironmentVariables(); // Environment variables override file settings\n        })\n        .ConfigureWebHostDefaults(webBuilder =>\n        {\n            webBuilder.UseStartup<Startup>();\n        });\n```\n\n## Advanced Features\n\nThe EnvironmentFile provider demonstrates several advanced features you might want to consider for your own providers:\n\n### 1. Reload on Change\n\nThe provider supports reloading configuration when the file changes:\n\n```csharp\nconfig.AddEnvironmentFile(\"reloadable.env\", reloadOnChange: true)\n```\n\nThis is implemented by leveraging the `FileConfigurationSource`'s `ReloadOnChange` property, which triggers a file system watcher.\n\n### 2. Prefix Handling\n\nThe provider can strip prefixes from keys:\n\n```csharp\nconfig.AddEnvironmentFile(\"with-prefix.env\", prefix: \"MyPrefix_\")\n```\n\nWhen using a prefix like `MyPrefix_`, keys like `MyPrefix_MyVariable` are loaded as just `MyVariable`.\n\n### 3. Data Transformation\n\nThe provider handles specific transformations:\n- Converting `__` to `:` for hierarchical configuration\n- Optionally removing quotes from values\n- Optionally trimming whitespace\n\n## Best Practices for Custom Configuration Providers\n\nBased on the EnvironmentFile provider, here are some best practices to consider:\n\n1. **Inherit from existing providers** when possible to reuse functionality\n2. **Make your provider optional** by default to avoid breaking the application if the configuration source is missing\n3. **Provide sensible defaults** but allow customization through parameters\n4. **Use extension methods** for a cleaner API\n5. **Add reload capabilities** when it makes sense for your source\n6. **Implement proper error handling** to gracefully handle malformed input\n7. **Separate parsing logic** from the provider itself for better maintainability\n\n## When to Use (and Not Use) Custom Providers\n\nCustom configuration providers are powerful, but they're not always necessary. Consider these guidelines:\n\n**Use a custom provider when:**\n- You need to support a specific file format not covered by built-in providers\n- You need to integrate with external configuration services\n- You want to add specialized processing or validation to configuration values\n\n**Consider alternatives when:**\n- You can achieve your goal with existing providers and a bit of code\n- Your source is only used in development, not in production\n- You're dealing with simple transformations that can be handled in startup code\n\n## Conclusion\n\nCustom configuration providers in .NET offer a powerful way to extend the configuration system to meet specific needs. The [Configuration.Extensions.EnvironmentFile](https://github.com/dariogriffo/Configuration.Extensions.EnvironmentFile) package demonstrates how to implement a complete provider that integrates smoothly with the .NET configuration pipeline.\n\nBy understanding the architecture and implementation patterns shown in this example, you can create your own providers to handle specialized configuration needs in your applications.\n\nWant to see more? Check out the full implementation of the [Environment File Provider on GitHub](https://github.com/dariogriffo/Configuration.Extensions.EnvironmentFile) or install the [NuGet package](https://www.nuget.org/packages/Configuration.Extensions.EnvironmentFile/) directly.\n\nHappy coding!"}, {"title": "How to Replace YARP Responses: Transforming Status Codes for Better Error Handling", "description": "Learn how to intercept and transform responses from upstream services using YARP (Yet Another Reverse Proxy) to create more semantically correct status codes and improve error handling in your microservices architecture.", "url": "/posts/how-to-replace-yarp-responses/", "content": "# How to Replace YARP Responses: Transforming Status Codes for Better Error Handling\n\n## Introduction\n\nWhen building modern applications with microservices architecture, one common challenge is ensuring consistent error handling across different services. Sometimes, upstream services return \"successful\" HTTP 200 responses containing validation errors in the response body - a pattern that can make error handling in client applications unnecessarily complex.\n\nToday, I'll show you how to use YARP (Yet Another Reverse Proxy) to intercept these responses and transform them into more appropriate HTTP status codes. Specifically, we'll look at a real-world example where we convert a 200 response containing validation errors into a 422 Unprocessable Entity response, making it more semantically correct and easier for clients to handle.\n\n> **Note:** This example works with YARP version 2.10 and later, which introduces improved response transformation capabilities. You can find the complete working example in my [GitHub repository](https://github.com/dariogriffo/yarp-body-replacement).\n\n## Understanding YARP Response Transforms\n\n[YARP](https://microsoft.github.io/reverse-proxy/) is a reverse proxy library developed by Microsoft that provides powerful capabilities for routing, load balancing, and transforming requests and responses. One of its key features is the ability to intercept and modify responses from upstream services before they reach the client.\n\nResponse transforms in YARP allow you to:\n- Modify response headers\n- Change status codes\n- Transform response bodies\n- Add or remove cookies\n- And much more\n\nThese transforms are applied through a pipeline, making it easy to chain multiple transformations together.\n\n## The Problem: 200 Responses with Validation Errors\n\nConsider this scenario: your backend service performs validation on submitted data and returns a JSON response like this:\n\n```json\n{\n  \"success\": false,\n  \"errors\": [\"Invalid first name\", \"Invalid last name\"]\n}\n```\n\nThe problem? This response comes with a 200 OK status code, even though it clearly represents a validation failure. According to HTTP semantics, a more appropriate status code would be 422 Unprocessable Entity.\n\n## The Solution: Creating a YARP Response Transform\n\nLet's look at how we can create a response transform to detect this pattern and convert it to a proper 422 response. We'll utilize YARP's powerful response transformation capabilities introduced in version 2.10 and later.\n\nAll the code shown in this post is available in my [GitHub repository](https://github.com/dariogriffo/yarp-body-replacement) as a fully functional example.\n\n### Step 1: Define the Validation Response Model\n\nFirst, let's create a class that represents the validation response structure we expect to receive:\n\n```csharp\nnamespace YourApplication.Features.ApiTransformations;\n\n/// <summary>\n/// Represents the response from the validation endpoint\n/// </summary>\npublic class ValidationResponse\n{\n    /// <summary>\n    /// Indicates whether the validation was successful\n    /// </summary>\n    public bool Success { get; set; }\n    \n    /// <summary>\n    /// List of error messages, only populated when Success is false\n    /// </summary>\n    public string[]? Errors { get; set; }\n}\n```\n\n### Step 2: Create a Response Transform Provider\n\nNext, let's create a transform provider that will be registered with YARP:\n\n```csharp\nusing System.Text;\nusing System.Text.Json;\nusing Yarp.ReverseProxy.Transforms;\nusing Yarp.ReverseProxy.Transforms.Builder;\n\nnamespace YourApplication.Features.ApiTransformations;\n\ninternal static class ValidationErrorTransformer\n{\n    public static void Transform(TransformBuilderContext builder) =>\n        builder.AddResponseTransform(async context =>\n        {\n            HttpContext httpContext = context.HttpContext;\n            HttpResponse response = httpContext.Response;\n            \n            // Only process responses with 200 status code\n            if (response.StatusCode != 200)\n            {\n                return;\n            }\n            \n            try\n            {\n                // Get the response content as a string\n                string responseBody = string.Empty;\n                \n                if (context.ProxyResponse is not null)\n                {\n                    responseBody = await context.ProxyResponse.Content.ReadAsStringAsync();\n                    \n                    // Try to deserialize the response directly into our expected format\n                    try \n                    {\n                        ValidationResponse? validationResponse = JsonSerializer.Deserialize<ValidationResponse>(\n                            responseBody, \n                            new JsonSerializerOptions { PropertyNameCaseInsensitive = true }\n                        );\n                        \n                        // Check if this is a validation error response\n                        if (validationResponse is { Success: false, Errors.Length: > 0 })\n                        {\n                            // Change status code to 422 Unprocessable Entity\n                            response.StatusCode = 422;\n                            \n                            // Replace the response body with only the array of errors\n                            context.SuppressResponseBody = true;\n                            \n                            // Serialize just the errors array\n                            string errorsJson = JsonSerializer.Serialize(validationResponse.Errors, \n                                new JsonSerializerOptions\n                                {\n                                    WriteIndented = true\n                                });\n                            \n                            // Set content type and write the new response\n                            response.ContentType = \"application/json\";\n                            response.ContentLength = Encoding.UTF8.GetByteCount(errorsJson);\n                            await response.WriteAsync(errorsJson);\n                        }\n                    }\n                    catch (JsonException)\n                    {\n                        // If we can't deserialize to our expected format, it's not a validation error response\n                    }\n                }\n            }\n            catch (Exception)\n            {\n                // In case of errors, return a 500 Internal Server Error\n                response.StatusCode = 500;\n            }\n        });\n}\n```\n\n### Step 3: Register the Transform with YARP\n\nNext, we register this transform in our application's startup code:\n\n```csharp\npublic class Program\n{\n    public static void Main(string[] args)\n    {\n        WebApplicationBuilder builder = WebApplication.CreateBuilder(args);\n\n        // Add YARP reverse proxy services\n        builder.Services.AddReverseProxy()\n            .LoadFromConfig(builder.Configuration.GetSection(\"ReverseProxy\"))\n            .AddTransforms(transformBuilder =>\n            {\n                // Add our validation error transformer\n                ValidationErrorTransformer.Transform(transformBuilder);\n            });\n\n        WebApplication app = builder.Build();\n\n        // Configure the HTTP request pipeline\n        if (app.Environment.IsDevelopment())\n        {\n            app.UseDeveloperExceptionPage();\n        }\n\n        app.UseRouting();\n        \n        // Map the reverse proxy endpoints\n        app.MapReverseProxy();\n\n        app.Run();\n    }\n}\n```\n\n### Step 4: Configure YARP Routes\n\nFinally, configure your YARP routes in `appsettings.json` to use this transform:\n\n```json\n{\n  \"ReverseProxy\": {\n    \"Routes\": {\n      \"api\": {\n        \"ClusterId\": \"apiCluster\",\n        \"Match\": {\n          \"Path\": \"/api/{**catch-all}\"\n        }\n      }\n    },\n    \"Clusters\": {\n      \"apiCluster\": {\n        \"Destinations\": {\n          \"destination1\": {\n            \"Address\": \"https://your-upstream-api.com/\"\n          }\n        }\n      }\n    }\n  }\n}\n```\n\n## How It Works\n\nLet's break down how this transform works:\n\n1. We add a response transform to the YARP pipeline that intercepts all responses from upstream services.\n2. For each response, we check if it has a 200 status code, which is the condition we're looking for.\n3. We read the response body from `context.ProxyResponse.Content`, which is available in YARP 2.10+.\n4. We deserialize the response into our `ValidationResponse` model with case-insensitive property matching.\n5. Using a pattern matching check (`validationResponse is { Success: false, Errors.Length: > 0 }`), we determine if it's a validation error.\n6. If it is, we change the status code to 422 and replace the response body with just the array of error messages.\n\nThe key improvement in this version is the direct access to `context.ProxyResponse.Content`, which eliminates the need for manual body manipulation used in older versions of YARP.\n\nFor a fully working implementation with tests, check out my [GitHub repository](https://github.com/dariogriffo/yarp-body-replacement).\n\n## Key Considerations\n\n### Performance\n\nReading and transforming response bodies can impact performance, so consider the following:\n\n- **Response Size**: For large responses, memory usage may increase.\n- **Content Encoding**: This example assumes uncompressed responses. For compressed responses (gzip, br), you'll need to handle decompression and compression.\n- **Caching**: If your application uses response caching, transformed responses will need appropriate cache headers.\n\n### Error Handling\n\nThe example includes basic error handling, but in a production system you might want to:\n\n- Have more granular error responses\n- Consider fallback strategies for different failure modes\n\n### Content Type Handling\n\nOur example assumes JSON responses. For other content types, you'll need different parsing strategies.\n\n## Advanced Scenarios\n\n### Selective Transformation\n\nYou might want to apply this transform only for certain routes or endpoints. You can modify the transform to check the request path or add route-specific configuration:\n\n```csharp\n// Only transform responses from certain paths\nif (!httpContext.Request.Path.StartsWithSegments(\"/api/users\"))\n{\n    return;\n}\n```\n\n### Combining with Other Transforms\n\nYARP allows you to chain multiple transforms. You might want to combine this with:\n\n- Request transforms that add headers or modify the request\n- Response header transforms\n- Authentication or authorization transforms\n\n### Dynamic Transformation Rules\n\nFor more complex scenarios, you could load transformation rules from configuration or a database:\n\n```csharp\n// Example: Load transformation rules from configuration\nvar transformRules = httpContext.RequestServices.GetRequiredService<IOptions<TransformRules>>().Value;\nif (!transformRules.EnableValidationTransform)\n{\n    return;\n}\n```\n\n## Conclusion\n\nYARP's response transforms provide a powerful way to ensure consistent error handling across your microservices architecture. By intercepting and modifying responses as demonstrated, you can create a more consistent API experience that follows HTTP semantics and makes error handling easier for client applications.\n\nThe specific example we explored - transforming a 200 response with validation errors into a 422 status code - is just one of many possibilities. The same approach can be used for various scenarios where upstream services don't follow your desired API conventions.\n\nRemember that while response transformation is powerful, it's also important to consider performance implications, especially for high-traffic applications. When possible, working with upstream service teams to standardize error handling is often the best long-term solution.\n\nFor a complete implementation of this example and more, visit my [GitHub repository](https://github.com/dariogriffo/yarp-body-replacement).\n\n## Resources\n\n- [GitHub Repository with Complete Example](https://github.com/dariogriffo/yarp-body-replacement)\n- [Official YARP Documentation](https://microsoft.github.io/reverse-proxy/)\n- [YARP GitHub Repository](https://github.com/microsoft/reverse-proxy)\n- [Response Transform Examples](https://learn.microsoft.com/en-us/aspnet/core/fundamentals/servers/yarp/transforms?view=aspnetcore-9.0)\n- [HTTP Status Codes](https://developer.mozilla.org/en-US/docs/Web/HTTP/Status)\n\n---\n\n*Have you used YARP for other interesting response transformations? Let me know in the comments!*"}, {"title": "Mastering Polymorphic Serialization with System.Text.Json", "description": "Explore how to effectively implement polymorphic serialization in .NET using System.Text.Json, with practical examples from a real-world event sourcing system.", "url": "/posts/polymorphic-serialization-system-text-json/", "content": "# Mastering Polymorphic Serialization with System.Text.Json\n\nOne of the most challenging aspects of working with JSON in .NET applications is handling polymorphic serialization—the ability to correctly serialize and deserialize objects from an inheritance hierarchy. With the introduction of `System.Text.Json` in .NET Core 3.0 and its continued evolution through .NET 8, Microsoft has significantly improved how we can handle these scenarios.\n\nIn this post, we'll explore how to implement polymorphic serialization with `System.Text.Json` using a real-world example from an event sourcing system where this technique is essential.\n\n## The Polymorphic Serialization Challenge\n\nWhen working with inheritance hierarchies in C#, we often need to serialize objects of derived types but deserialize them through a base class or interface reference. This presents a challenge: how can the deserializer know which concrete type to instantiate when all it sees is the base type?\n\nThis is especially important in domains like:\n\n- Event sourcing systems\n- Plugin architectures\n- Domain-driven design with rich inheritance models\n- Message-based systems\n\nLet's explore a practical solution using a real-world event store implementation.\n\n## Real-World Example: Event Sourcing System\n\nIn event sourcing systems, we typically work with a hierarchy of event types that all inherit from a common base. When storing and retrieving these events, we need to make sure the right concrete types are created during deserialization.\n\nHere's the high-level structure of our event model:\n\n```csharp\n// Base event type\npublic abstract class Event\n{\n    public Guid Id { get; set; }\n    public DateTime Timestamp { get; set; }\n}\n\n// More specific event type\npublic abstract class AggregateEvent : Event\n{\n    public string AggregateId { get; set; }\n    public int Version { get; set; }\n}\n\n// Concrete event types\npublic class UserCreatedEvent : AggregateEvent\n{\n    public string Username { get; set; }\n    public string Email { get; set; }\n}\n\npublic class OrderPlacedEvent : AggregateEvent\n{\n    public string OrderId { get; set; }\n    public decimal Amount { get; set; }\n    public List<OrderItem> Items { get; set; }\n}\n```\n\n## Solution 1: Using Type Discriminators with JsonDerivedType Attribute\n\nStarting with .NET 7, `System.Text.Json` offers built-in support for polymorphic serialization through the `JsonDerivedType` attribute.\n\nHere's how we can set up our event model to use this approach:\n\n```csharp\nusing System.Text.Json.Serialization;\n\n[JsonDerivedType(typeof(AggregateEvent), nameof(AggregateEvent))]\n[JsonDerivedType(typeof(UserCreatedEvent), nameof(UserCreatedEvent))]\n[JsonDerivedType(typeof(OrderPlacedEvent), nameof(OrderPlacedEvent))]\npublic abstract class Event\n{\n    public Guid Id { get; set; }\n    public DateTime Timestamp { get; set; }\n}\n```\n\nWith this setup, when serializing a derived type through a base class reference, the JSON output includes a type discriminator:\n\n```json\n{\n  \"$type\": \"UserCreatedEvent\",\n  \"AggregateId\": \"user-123\",\n  \"Version\": 1,\n  \"Username\": \"johndoe\",\n  \"Email\": \"<EMAIL>\",\n  \"Id\": \"d8a7d8e1-ef3b-4acd-8d2e-ac4f3bc5c913\",\n  \"Timestamp\": \"2025-04-09T12:00:00Z\"\n}\n```\n\nThe `$type` property is a special field that indicates the concrete type, allowing the deserializer to create the correct instance.\n\n## Solution 2: Custom Type Resolution with EventsTypeResolver\n\nFor more complex scenarios or when you need more control over the serialization process, you can implement a custom `JsonTypeInfoResolver`. This approach is particularly useful when you're working with types across assembly boundaries or can't modify the class definitions.\n\nLet's look at a real implementation from an event store system:\n\n```csharp\ninternal class EventsTypeResolver : DefaultJsonTypeInfoResolver\n{\n    private readonly IEnumerable<Assembly> _eventsAssemblies;\n\n    public EventsTypeResolver(IEnumerable<Assembly> eventsAssemblies)\n    {\n        _eventsAssemblies = eventsAssemblies;\n    }\n\n    public override JsonTypeInfo GetTypeInfo(Type type, JsonSerializerOptions options)\n    {\n        JsonTypeInfo jsonTypeInfo = base.GetTypeInfo(type, options);\n\n        Type aggregateEventType = typeof(AggregateEvent);\n        Type baseEventType = typeof(Event);\n        Type stateType = typeof(StateUpdated<>);\n\n        if (\n            jsonTypeInfo.Type.IsGenericType\n            && jsonTypeInfo.Type.GetGenericTypeDefinition() == stateType\n        )\n        {\n            RegisterStateUpdated(jsonTypeInfo);\n            return jsonTypeInfo;\n        }\n\n        RegisterPolymorphicType(\n            _eventsAssemblies,\n            baseEventType,\n            jsonTypeInfo,\n            (t) =>\n                !t.IsAbstract\n                && t.IsSubclassOf(baseEventType)\n                && !t.IsSubclassOf(aggregateEventType)\n        );\n\n        RegisterPolymorphicType(\n            _eventsAssemblies,\n            aggregateEventType,\n            jsonTypeInfo,\n            (t) => !t.IsAbstract && t.IsSubclassOf(aggregateEventType)\n        );\n\n        return jsonTypeInfo;\n    }\n\n    private void RegisterStateUpdated(JsonTypeInfo jsonTypeInfo)\n    {\n        Type derivedType = jsonTypeInfo.Type;\n        string typeDiscriminator = derivedType.GetTypeDiscriminator()!;\n        int from = typeDiscriminator.IndexOf(\", Version\", StringComparison.Ordinal);\n        int to = typeDiscriminator.IndexOf(\"]\", StringComparison.Ordinal);\n        typeDiscriminator = typeDiscriminator.Replace(\n            typeDiscriminator.Substring(from, to - from + 1),\n            string.Empty\n        );\n        jsonTypeInfo.PolymorphismOptions = new JsonPolymorphismOptions\n        {\n            TypeDiscriminatorPropertyName = TypeExtensions.EventTypeDiscriminatorName,\n            IgnoreUnrecognizedTypeDiscriminators = false,\n            UnknownDerivedTypeHandling = JsonUnknownDerivedTypeHandling.FailSerialization,\n            DerivedTypes = { new JsonDerivedType(derivedType, typeDiscriminator) }\n        };\n    }\n\n    private void RegisterPolymorphicType(\n        IEnumerable<Assembly> assembliesToScan,\n        Type baseEventType,\n        JsonTypeInfo jsonTypeInfo,\n        Predicate<Type> predicate\n    )\n    {\n        if (jsonTypeInfo.Type != baseEventType)\n        {\n            return;\n        }\n\n        List<Type> baseScannedTypes = assembliesToScan\n            .SelectMany(assembly => assembly.GetTypes().Where(t => predicate(t)).ToList())\n            .ToList();\n\n        if (baseScannedTypes.Count == 0)\n        {\n            //If this is removed System.Text.Json throws exception\n            return;\n        }\n\n        List<JsonDerivedType> derivedTypes = baseScannedTypes\n            .Select(type => new JsonDerivedType(type, type.GetTypeDiscriminator()))\n            .ToList();\n\n        jsonTypeInfo.PolymorphismOptions = new JsonPolymorphismOptions\n        {\n            TypeDiscriminatorPropertyName = TypeExtensions.EventTypeDiscriminatorName,\n            IgnoreUnrecognizedTypeDiscriminators = false,\n            UnknownDerivedTypeHandling = JsonUnknownDerivedTypeHandling.FailSerialization,\n            DerivedTypes = { }\n        };\n\n        foreach (JsonDerivedType derivedType in derivedTypes)\n        {\n            jsonTypeInfo.PolymorphismOptions.DerivedTypes.Add(derivedType);\n        }\n    }\n}\n```\n\nIn this implementation:\n\n1. The resolver automatically discovers event types from specified assemblies\n2. It registers derived types with their type discriminators\n3. It handles both direct subclasses of `Event` and `AggregateEvent`\n4. It configures the polymorphism options for each registered type\n\nThe `TypeExtensions` class provides a consistent way to generate type discriminators:\n\n```csharp\ninternal static class TypeExtensions\n{\n    internal const string EventTypeDiscriminatorName = \"$type\";\n\n    internal static string GetTypeDiscriminator(this Type type) =>\n        $\"{type.FullName!}, {type.Assembly.GetName().Name}\";\n}\n```\n\nThis generates type discriminators in the format `Namespace.TypeName, AssemblyName`, which provides a fully qualified way to identify types across assembly boundaries.\n\n## Using the Type Resolver\n\nTo apply this custom type resolution, you configure your JsonSerializerOptions:\n\n```csharp\npublic static class PaytentlyEventsJsonSerializerOptions\n{\n    public static JsonSerializerOptions WithTypeInfoResolver(EventsTypeResolver typeResolver)\n    {\n        return new JsonSerializerOptions\n        {\n            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,\n            TypeInfoResolver = typeResolver,\n            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull\n        };\n    }\n\n    public static JsonSerializerOptions Default => new()\n    {\n        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,\n        DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull\n    };\n}\n```\n\nAnd then use it in your serialization operations:\n\n```csharp\nAssembly[] assemblies = new[] { typeof(UserCreatedEvent).Assembly };\nJsonSerializerOptions options = PaytentlyEventsJsonSerializerOptions.WithTypeInfoResolver(\n    new EventsTypeResolver(assemblies)\n);\n\n// Serialize\nAggregateEvent eventObj = new UserCreatedEvent { /* properties */ };\nstring json = JsonSerializer.Serialize<AggregateEvent>(eventObj, options);\n\n// Deserialize\nAggregateEvent deserializedEvent = JsonSerializer.Deserialize<AggregateEvent>(json, options);\n```\n\n\n## Best Practices for Polymorphic Serialization\n\nBased on these real-world implementations, here are some best practices for polymorphic serialization:\n\n1. **Use the right approach for your scenario:**\n   - For simple hierarchies where you own the code, use `JsonDerivedType` attributes\n   - For complex hierarchies or third-party types, implement a custom `JsonTypeInfoResolver`\n\n2. **Choose a consistent discriminator format:**\n   - Use simple string discriminators when possible (e.g., type names)\n   - For more complex systems, consider fully-qualified type names\n\n3. **Cache type information for performance:**\n   - Create dictionaries mapping discriminators to type information\n   - Reuse serialization options when possible\n\n4. **Handle error cases gracefully:**\n   - Configure what happens with unknown derived types\n   - Decide whether to ignore unrecognized discriminators or fail\n\n5. **Consider security implications:**\n   - Be careful with type discriminators that allow arbitrary type loading\n   - Validate types against a whitelist when deserializing from untrusted sources\n\n## Conclusion\n\nPolymorphic serialization is a powerful technique that enables flexible and type-safe serialization across inheritance hierarchies. With the improvements in `System.Text.Json` since .NET 7, implementing this pattern has become more straightforward.\n\nThe real-world example we've explored demonstrates how to implement this in a production-grade event sourcing system. By using type discriminators and custom type resolvers, we can achieve robust serialization and deserialization of polymorphic types.\n\nWhether you're building an event store, message-based system, or any application that needs to handle polymorphic data, these techniques will help you implement clean, maintainable, and performant serialization logic.\n\nRemember that while the examples shown are from an event sourcing system, the same techniques apply to many other domains where polymorphic serialization is needed.\n\n## Further Reading\n\n- [System.Text.Json Polymorphism Documentation](https://learn.microsoft.com/en-us/dotnet/standard/serialization/system-text-json/polymorphism)\n- [JsonTypeInfoResolver API](https://learn.microsoft.com/en-us/dotnet/api/system.text.json.serialization.metadata.jsontypeinforesolver)\n- [Event Sourcing Patterns](https://docs.microsoft.com/en-us/azure/architecture/patterns/event-sourcing)"}, {"title": "The Ultimate Guide to Creating Debian Packages", "description": "Learn how to create and maintain Debian packages for multiple distributions using Docker, with real-world automation techniques and best practices.", "url": "/posts/ultimate-guide-debian-packaging/", "content": "# The Ultimate Guide to Creating Debian Packages\n\nIf you're a developer who works with Linux systems, particularly Debian-based distributions, creating packages for your software is a powerful skill. Debian packages provide a standardized way to distribute applications, making installation, upgrading, and removal seamless for users. However, the packaging process can seem intimidating with its specific file structure and conventions.\n\nIn this comprehensive guide, I'll walk you through creating production-ready Debian packages, including how to target multiple distributions efficiently using Docker. Rather than covering theoretical examples, I'll share real automation techniques from my own projects like [uv-debian](https://github.com/dariogriffo/uv-debian) and [lazygit-debian](https://github.com/dariogriffo/lazygit-debian).\n\n## Why Create Debian Packages?\n\nBefore diving into the technical details, let's understand why Debian packages are worth the effort:\n\n1. **Simplified installation** - Users can install your software with a simple `apt install` command\n2. **Dependency management** - Automatically handle prerequisite packages\n3. **Clean upgrades and removals** - Provide a consistent experience for package lifecycle\n4. **System integration** - Properly register services, documentation, and configuration files\n5. **Distribution channel** - Enable distribution through official or custom repositories\n\n## Understanding the Debian Package Structure\n\nA Debian package (.deb file) is essentially an archive containing your application files and metadata. The basic structure includes:\n\n```\npackage-name-version/\n├── DEBIAN/\n│   ├── control       # Package metadata\n│   ├── preinst       # Pre-installation script (optional)\n│   ├── postinst      # Post-installation script (optional)\n│   ├── prerm         # Pre-removal script (optional)\n│   └── postrm        # Post-removal script (optional)\n└── usr/\n    ├── bin/          # Executable files\n    ├── lib/          # Library files\n    ├── share/\n    │   ├── doc/      # Documentation\n    │   └── man/      # Man pages\n    └── ...           # Other directories following FHS\n```\n\n### Essential Files Explained\n\nThe most critical file is `DEBIAN/control`, which contains package metadata. Here's an example from my uv package:\n\n```\nSource: uv\nSection: utils\nPriority: optional\nMaintainer: Dario Griffo <<EMAIL>>\nHomepage: https://github.com/astral-sh/uv\nPackage: uv\nVersion: 0.7.0-1+bookworm\nArchitecture: amd64\nDepends: \nDescription: An extremely fast Python package and project manager, written in Rust.\n```\n\nLet's break down the key fields:\n\n- **Source**: The name of the source package\n- **Section**: The package category (common values include 'utils', 'net', 'devel')\n- **Priority**: Usually 'optional' for most third-party packages\n- **Maintainer**: Your name and email\n- **Package**: The package name (often the same as Source)\n- **Version**: Following the format `[upstream-version]-[debian-revision]+[distribution]`\n- **Architecture**: Target architecture ('amd64', 'arm64', 'all', etc.)\n- **Depends**: Required packages for your software to run\n- **Description**: A brief description followed by a longer multi-line description\n\n## Creating a Basic Debian Package Manually\n\nLet's create a package for `lazygit`, a terminal UI for Git commands:\n\n1. **Create the package directory structure**:\n\n```bash\nmkdir -p lazygit-0.40.0/DEBIAN\nmkdir -p lazygit-0.40.0/usr/bin\nmkdir -p lazygit-0.40.0/usr/share/doc/lazygit\n```\n\n2. **Create the control file**:\n\n```bash\ncat > lazygit-0.40.0/DEBIAN/control << EOF\nSource: lazygit\nSection: utils\nPriority: optional\nMaintainer: Dario Griffo <<EMAIL>>\nHomepage: https://github.com/jesseduffield/lazygit\nPackage: lazygit\nVersion: 0.40.0-1\nArchitecture: amd64\nDepends: git\nDescription: Simple terminal UI for git commands\n A simple terminal UI for git commands, written in Go with the gocui library.\n Main features: Easily manage your git workflow from a terminal-based UI.\nEOF\n```\n\n3. **Add executable file**:\n   Download the binary from the official release and place it in the correct location:\n\n```bash\n# Download the lazygit binary\nwget -O lazygit-0.40.0/usr/bin/lazygit \\\n  https://github.com/jesseduffield/lazygit/releases/download/v0.40.0/lazygit_0.40.0_Linux_x86_64.tar.gz\ntar -xzf lazygit-0.40.0/usr/bin/lazygit\ncp lazygit lazygit-0.40.0/usr/bin/\nchmod +x lazygit-0.40.0/usr/bin/lazygit\n```\n\n4. **Add documentation**:\n\n```bash\ncat > lazygit-0.40.0/usr/share/doc/lazygit/changelog.Debian << EOF\nlazygit (0.40.0-1) unstable; urgency=low\n\n  * Packaging lazygit version 0.40.0\n\n -- Dario Griffo <<EMAIL>>  $(date -R)\nEOF\ngzip -9 -n lazygit-0.40.0/usr/share/doc/lazygit/changelog.Debian\n\ncat > lazygit-0.40.0/usr/share/doc/lazygit/copyright << EOF\nFormat: https://www.debian.org/doc/packaging-manuals/copyright-format/1.0/\nUpstream-Name: lazygit\nSource: https://github.com/jesseduffield/lazygit\n\nFiles: *\nCopyright: $(date +%Y) Jesse Duffield <<EMAIL>>\nLicense: MIT\n Permission is hereby granted, free of charge, to any person obtaining a copy\n of this software and associated documentation files (the \"Software\"), to deal\n in the Software without restriction...\nEOF\n```\n\n5. **Build the package**:\n\n```bash\ndpkg-deb --build lazygit-0.40.0\n```\n\nThis command produces a `.deb` file that can be installed using `dpkg -i lazygit-0.40.0.deb`.\n\n## Targeting Multiple Debian Distributions with Docker\n\nOne of the challenges in Debian packaging is supporting multiple distributions (Bookworm, Trixie, Sid, etc.). Each distribution might have different dependencies or packaging requirements.\n\nI've solved this problem in my [uv-debian](https://github.com/dariogriffo/uv-debian) and [lazygit-debian](https://github.com/dariogriffo/lazygit-debian) repositories by using Docker to build packages for different distributions. Let's see how I've set this up:\n\n### Creating a Dockerfile for Package Building\n\nHere's the Dockerfile I use for building the `uv` package:\n\n```dockerfile\nARG DEBIAN_DIST=bookworm\nFROM debian:$DEBIAN_DIST\n\nARG DEBIAN_DIST\nARG UV_VERSION\nARG BUILD_VERSION\nARG FULL_VERSION\n\nRUN apt update && apt install -y wget\nRUN mkdir -p /output/usr/bin\nRUN mkdir -p /output/usr/share/doc/uv\nRUN cd /output/usr/bin && wget https://github.com/astral-sh/uv/releases/download/${UV_VERSION}/uv-x86_64-unknown-linux-musl.tar.gz && tar -xf uv-x86_64-unknown-linux-musl.tar.gz && rm -f uv-x86_64-unknown-linux-musl.tar.gz\nRUN mkdir -p /output/DEBIAN\n\nCOPY output/DEBIAN/control /output/DEBIAN/\nCOPY output/copyright /output/usr/share/doc/uv/\nCOPY output/changelog.Debian /output/usr/share/doc/uv/\nCOPY output/README.md /output/usr/share/doc/uv/\n\nRUN sed -i \"s/DIST/$DEBIAN_DIST/\" /output/usr/share/doc/uv/changelog.Debian\nRUN sed -i \"s/FULL_VERSION/$FULL_VERSION/\" /output/usr/share/doc/uv/changelog.Debian\nRUN sed -i \"s/DIST/$DEBIAN_DIST/\" /output/DEBIAN/control\nRUN sed -i \"s/UV_VERSION/$UV_VERSION/\" /output/DEBIAN/control\nRUN sed -i \"s/BUILD_VERSION/$BUILD_VERSION/\" /output/DEBIAN/control\n\nRUN dpkg-deb --build /output /uv_${FULL_VERSION}.deb\n```\n\n### Creating a Build Script\n\nHere's the build script I use to automate building packages for multiple distributions:\n\n```bash\n#!/bin/bash\nUV_VERSION=$1\nBUILD_VERSION=$2\ndeclare -a arr=(\"bookworm\" \"trixie\" \"sid\")\nfor i in \"${arr[@]}\"\ndo\n  DEBIAN_DIST=$i\n  FULL_VERSION=$UV_VERSION-${BUILD_VERSION}+${DEBIAN_DIST}_amd64\n  docker build . -t uv-$DEBIAN_DIST  --build-arg DEBIAN_DIST=$DEBIAN_DIST --build-arg UV_VERSION=$UV_VERSION --build-arg BUILD_VERSION=$BUILD_VERSION --build-arg FULL_VERSION=$FULL_VERSION\n  id=\"$(docker create uv-$DEBIAN_DIST)\"\n  docker cp $id:/uv_$FULL_VERSION.deb - > ./uv_$FULL_VERSION.deb\n  tar -xf ./uv_$FULL_VERSION.deb\ndone\n```\n\nRunning the script with version parameters is simple:\n\n```bash\n./build.sh 0.7.0 1\n```\n\nThis will produce three packages:\n- `uv_0.7.0-1+bookworm_amd64.deb`\n- `uv_0.7.0-1+trixie_amd64.deb`\n- `uv_0.7.0-1+sid_amd64.deb`\n\n### Preparing Template Files\n\nFor this to work, I prepare template files with placeholders. Here's what the `control` file looks like for the uv package:\n\n```\nSource: uv\nSection: utils\nPriority: optional\nMaintainer: Dario Griffo <<EMAIL>>\nHomepage: https://github.com/astral-sh/uv\nPackage: uv\nVersion: UV_VERSION-BUILD_VERSION+DIST\nArchitecture: amd64\nDepends: \nDescription: An extremely fast Python package and project manager, written in Rust.\n```\n\nAnd the `changelog.Debian` template:\n\n```\nuv (FULL_VERSION) DIST; urgency=low\n\n \n  \n\n```\n\nThe build process automatically replaces placeholders like `UV_VERSION`, `BUILD_VERSION`, `DIST`, and `FULL_VERSION` with the appropriate values for each distribution.\n\n## Real-world Example: Packaging Multiple Applications\n\nI'm maintaining packages for several tools, including:\n\n1. **uv** - A fast Python package manager written in Rust ([uv-debian repository](https://github.com/dariogriffo/uv-debian))\n2. **lazygit** - A terminal UI for Git ([lazygit-debian repository](https://github.com/dariogriffo/lazygit-debian))\n\nBoth projects follow a similar structure, with a Dockerfile that:\n\n1. Takes the distribution name as an argument\n2. Downloads the binary from the original project releases\n3. Sets up the package structure\n4. Uses template files with placeholders\n5. Builds the package for the specific Debian distribution\n\nThis approach allows me to:\n\n1. Quickly update packages when a new upstream version is released\n2. Support multiple Debian distributions from a single codebase\n3. Automate the build process completely\n4. Maintain high-quality packages with minimal effort\n\n## Best Practices for Debian Packaging\n\nBased on my experience maintaining packages like uv and lazygit, here are some best practices:\n\n1. **Follow Debian Policy**: Familiarize yourself with the [Debian Policy Manual](https://www.debian.org/doc/debian-policy/) for guidelines.\n\n2. **Version Carefully**: Use a clear versioning scheme that allows for future updates. I use the format `upstream-version-debian-revision+distribution` (e.g., `0.7.0-1+bookworm`).\n\n3. **Automate Building**: Use scripts to automate the build process, making it reproducible.\n\n4. **Test Your Packages**: Install and test packages in clean environments to verify they work correctly.\n\n5. **Include Proper Documentation**: Always provide copyright, changelog, and README files.\n\n6. **Use Lintian**: Run `lintian` on your packages to check for common issues:\n   ```bash\n   lintian uv_0.7.0-1+bookworm_amd64.deb\n   ```\n\n7. **Sign Your Packages**: Use GPG to sign packages for improved security:\n   ```bash\n   dpkg-sig --sign builder uv_0.7.0-1+bookworm_amd64.deb\n   ```\n\n8. **Consider Continuous Integration**: Set up CI/CD pipelines to automatically build packages when your source code changes. My repositories use GitHub Actions for this purpose.\n\n## Advanced Topics\n\n### Handling Dependencies\n\nDependencies are specified in the `control` file's `Depends` field. You can specify version requirements using operators:\n\n```\nDepends: libc6 (>= 2.14), python3 (>= 3.9), libgcc1 (>= 1:4.2)\n```\n\nFor lazygit, the only dependency is git:\n\n```\nDepends: git\n```\n\n### Maintainer Scripts\n\nFor more complex packages, you might need maintainer scripts:\n\n- **preinst**: Runs before unpacking the package\n- **postinst**: Runs after unpacking\n- **prerm**: Runs before removing\n- **postrm**: Runs after removing\n\nHere's a simple example of a `postinst` script:\n\n```bash\n#!/bin/bash\nset -e\n\n# Create a configuration file if it doesn't exist\nif [ ! -f /etc/my-package/config.yaml ]; then\n    mkdir -p /etc/my-package\n    echo \"# Default configuration\" > /etc/my-package/config.yaml\n    echo \"enabled: true\" >> /etc/my-package/config.yaml\nfi\n\n# Reload systemd if present\nif command -v systemctl >/dev/null; then\n    systemctl daemon-reload\nfi\n\nexit 0\n```\n\n### Creating a Local Repository\n\nWhile a comprehensive guide to hosting your own repository will come in a future post, here's a quick overview of the process I use for my [debian.griffo.io](https://debian.griffo.io) repository:\n\n1. **Create a directory structure**:\n   ```bash\n   mkdir -p repo/dists/{bookworm,trixie,sid}/{main,contrib,non-free}/binary-amd64\n   ```\n\n2. **Copy packages to the appropriate directories**:\n   ```bash\n   cp uv_0.7.0-1+bookworm_amd64.deb repo/dists/bookworm/main/binary-amd64/\n   ```\n\n3. **Generate package indexes**:\n   ```bash\n   cd repo\n   apt-ftparchive packages dists/bookworm/main/binary-amd64 > dists/bookworm/main/binary-amd64/Packages\n   gzip -9c dists/bookworm/main/binary-amd64/Packages > dists/bookworm/main/binary-amd64/Packages.gz\n   ```\n\n4. **Make the repository accessible**:\n   ```bash\n   # Add to sources.list.d\n   echo \"deb https://debian.griffo.io/apt $(lsb_release -sc) main\" | sudo tee /etc/apt/sources.list.d/debian.griffo.io.list\n   ```\n\n## Conclusion\n\nCreating Debian packages is a valuable skill that allows you to distribute your software in a standardized way. By using Docker for package building, you can easily target multiple Debian distributions with minimal effort, as demonstrated in my [uv-debian](https://github.com/dariogriffo/uv-debian) and [lazygit-debian](https://github.com/dariogriffo/lazygit-debian) repositories.\n\nIn this guide, we've covered:\n\n1. The structure and essential files of a Debian package\n2. How to create a basic package manually\n3. Using Docker to target multiple distributions\n4. Real-world examples with automation scripts\n5. Best practices for package maintenance\n\nIf you're interested in learning how to host these packages in your own Debian repository, check out my follow-up guide: [The Ultimate Guide to Self-Hosting a Debian Repository](/posts/ultimate-guide-debian-repository-hosting/), which shows you how to set up a repository that allows users to install your packages using `apt` and receive automatic updates.\n\nI hope this guide helps you streamline your Debian packaging process. If you have any questions or suggestions, feel free to reach out!"}, {"title": "The Ultimate Guide to Self-Hosting a Debian Repository", "description": "Learn how to set up and maintain your own Debian repository for multiple distributions using reprepro, with real-world examples from my personal setup.", "url": "/posts/ultimate-guide-debian-repository-hosting/", "content": "# The Ultimate Guide to Self-Hosting a Debian Repository\n\nIn my [previous guide on creating Debian packages](/posts/ultimate-guide-debian-packaging/), I walked through the process of building packages for multiple Debian distributions using Docker. But creating packages is only half the battle - distributing them efficiently to users requires a properly configured Debian repository. \n\nThis guide will show you how to set up your own Debian repository using `reprepro`, a tool that simplifies repository management. I'll share my actual setup from [debian.griffo.io](https://debian.griffo.io), which hosts unofficial packages for tools like [lazygit](https://github.com/jesseduffield/lazygit/), [lazydocker](https://github.com/jesseduffield/lazydocker/), [uv](https://github.com/astral-sh/uv), and more.\n\n## Understanding Debian Repository Structure\n\nBefore diving into the setup, let's understand the structure of a Debian repository:\n\n```\ndebian-repository/\n├── dists/                         # Distribution release directories\n│   ├── bookworm/                  # Debian 12 (Stable)\n│   │   ├── InRelease              # Combined Release file with signature\n│   │   ├── Release                # Repository metadata\n│   │   ├── Release.gpg            # Detached signature for Release file\n│   │   └── main/                  # Component directory\n│   │       └── binary-amd64/      # Architecture-specific directory\n│   │           ├── Packages       # Package index\n│   │           ├── Packages.gz    # Compressed package index\n│   │           └── Release        # Component-specific metadata\n│   ├── trixie/                    # Debian 13 (Testing)\n│   └── sid/                       # Debian Unstable\n└── pool/                          # Package storage\n    └── main/                      # Main component\n        ├── l/                     # First letter of package name\n        │   ├── lazygit/           # Package directory\n        │   │   ├── lazygit_0.40.2-1+bookworm_amd64.deb  # Package for Bookworm\n        │   │   ├── lazygit_0.40.2-1+trixie_amd64.deb    # Package for Trixie\n        │   │   └── lazygit_0.40.2-1+sid_amd64.deb       # Package for Sid\n        │   └── lazydocker/        # Another package directory\n        └── u/\n            └── uv/                # Another package directory\n```\n\nThis structure enables APT to efficiently locate and download packages for the correct distribution and architecture.\n\n## Setting Up Your Repository with reprepro\n\n`reprepro` is a tool that manages the repository structure for you, handling the creation of directories and index files. Let's walk through the setup process.\n\n### 1. Install reprepro\n\nFirst, install reprepro on your system:\n\n```bash\nsudo apt install reprepro gnupg\n```\n\n### 2. Create a GPG key for signing\n\nDebian repositories should be signed with a GPG key to ensure authenticity:\n\n```bash\ngpg --full-generate-key\n```\n\nSelect RSA (sign only) with a key size of 4096 bits, and follow the prompts to create your key. Once created, note your key ID:\n\n```bash\ngpg --list-keys --keyid-format long\n```\n\nExport your public key to share with users:\n\n```bash\ngpg --armor --export YOUR_KEY_ID > repository-key.asc\n```\n\n### 3. Set up the repository structure\n\nCreate the base directories:\n\n```bash\nmkdir -p debian-repo/{apt,deb,reprepro/{conf,db}}\n```\n\nConfigure reprepro by creating a `distributions` file:\n\n```bash\ncat > debian-repo/reprepro/conf/distributions << EOF\nCodename: bookworm\nArchitectures: amd64\nComponents: main \nDescription: Apt repository for unofficial packages \nSignWith: YOUR_KEY_ID\n\nCodename: trixie\nArchitectures: amd64\nComponents: main \nDescription: Apt repository for unofficial packages\nSignWith: YOUR_KEY_ID\n\nCodename: sid\nArchitectures: amd64\nComponents: main \nDescription: Apt repository for unofficial packages\nSignWith: YOUR_KEY_ID\nEOF\n```\n\nCreate directories for each distribution:\n\n```bash\nmkdir -p debian-repo/deb/{bookworm,trixie,sid}\n```\n\n### 4. Add packages to the repository\n\nTo add packages to your repository, place your `.deb` files in the appropriate distribution directory:\n\n```bash\n# Example for lazygit\ncp lazygit_0.40.2-1+bookworm_amd64.deb debian-repo/deb/bookworm/\ncp lazygit_0.40.2-1+trixie_amd64.deb debian-repo/deb/trixie/\ncp lazygit_0.40.2-1+sid_amd64.deb debian-repo/deb/sid/\n```\n\nThen use reprepro to include them in the repository:\n\n```bash\ncd debian-repo\nreprepro --dbdir reprepro/db --confdir reprepro/conf -C main includedeb bookworm deb/bookworm/*.deb\nreprepro --dbdir reprepro/db --confdir reprepro/conf -C main includedeb trixie deb/trixie/*.deb\nreprepro --dbdir reprepro/db --confdir reprepro/conf -C main includedeb sid deb/sid/*.deb\n```\n\nThis command adds the packages to the repository and creates all necessary index files automatically.\n\n### 5. Sign the Release files\n\nreprepro will automatically sign the Release files, but if needed, you can manually sign them:\n\n```bash\ncd debian-repo/apt/dists/bookworm\ncat Release | gpg -s --default-key YOUR_KEY_ID -abs > Release.gpg\ncat Release | gpg -s --default-key YOUR_KEY_ID --clearsign > InRelease\n```\n\nRepeat for each distribution.\n\n## Automating Repository Management\n\nTo streamline repository management, I've created several scripts that automate the process. Here's how I've set up automation for my repository at [debian.griffo.io](https://debian.griffo.io):\n\n### Download script\n\nThis script downloads package files from GitHub releases:\n\n```bash\n#!/bin/bash\n# download_deb_file.sh\nREPO=$1\nPACKAGE_VERSION=$2\nBUILD_VERSION=$3\nPACKAGE_NAME=$4\nPWD=`pwd`\ndeclare -a arr=(\"bookworm\" \"trixie\" \"sid\")\nfor i in \"${arr[@]}\"\ndo\n    DEBIAN_DIST=$i\n    cd deb/${DEBIAN_DIST}\n    wget https://github.com/dariogriffo/${REPO}/releases/download/${PACKAGE_VERSION}+${BUILD_VERSION}/${PACKAGE_NAME}_${PACKAGE_VERSION}-${BUILD_VERSION}+${DEBIAN_DIST}_amd64.deb\n    cd ../..\ndone\n```\n\n### Generate index script\n\nThis script updates the repository indexes after adding new packages:\n\n```bash\n#!/bin/bash\n# generate_index.sh\ncurrent=`pwd`\nreprepro=$current/reprepro\ncd apt\nreprepro --dbdir $reprepro/db --confdir $reprepro/conf -C main includedeb bookworm $current/deb/bookworm/*deb\nreprepro --dbdir $reprepro/db --confdir $reprepro/conf -C main includedeb trixie $current/deb/trixie/*deb\nreprepro --dbdir $reprepro/db --confdir $reprepro/conf -C main includedeb sid $current/deb/sid/*deb\ncd dists/bookworm\ncat Release | gpg -s --default-key YOUR_KEY_ID -abs > Release.gpg\ncd -\ncd dists/trixie\ncat Release | gpg -s --default-key YOUR_KEY_ID -abs > Release.gpg\ncd -\ncd dists/sid\ncat Release | gpg -s --default-key YOUR_KEY_ID -abs > Release.gpg\ncd -\ncd ..\n```\n\n### Package update script\n\nFor each package, I have a specific update script:\n\n```bash\n#!/bin/bash\n# update_uv.sh\nREPO=uv-debian\nPACKAGE_VERSION=$1\nBUILD_VERSION=${2:-1}\nPACKAGE_NAME=uv\n./download_deb_file.sh ${REPO} ${PACKAGE_VERSION} ${BUILD_VERSION} ${PACKAGE_NAME}\n./generate_index.sh\ngit add .\ngit commit -m \"Update uv to ${PACKAGE_VERSION}\"\ngit push -u origin main\n```\n\nWith these scripts, updating a package is as simple as:\n\n```bash\n./update_uv.sh 0.7.2 1\n```\n\nThis downloads the package files, updates the repository indexes, and commits the changes to git.\n\n## Hosting Your Repository\n\nOnce your repository is set up, you need to host it so users can access it. Here are three common options:\n\n### Option 1: Apache Web Server\n\n1. Install Apache:\n   ```bash\n   sudo apt install apache2\n   ```\n\n2. Configure a virtual host:\n   ```bash\n   sudo nano /etc/apache2/sites-available/debian-repo.conf\n   ```\n\n   Add the following configuration:\n   ```apache\n   <VirtualHost *:80>\n       ServerName debian.yourdomain.com\n       DocumentRoot /path/to/debian-repo/apt\n       \n       <Directory /path/to/debian-repo/apt>\n           Options Indexes FollowSymLinks\n           AllowOverride None\n           Require all granted\n       </Directory>\n       \n       ErrorLog ${APACHE_LOG_DIR}/debian-repo-error.log\n       CustomLog ${APACHE_LOG_DIR}/debian-repo-access.log combined\n   </VirtualHost>\n   ```\n\n3. Enable the site and restart Apache:\n   ```bash\n   sudo a2ensite debian-repo.conf\n   sudo systemctl restart apache2\n   ```\n\n4. Set up HTTPS with Let's Encrypt:\n   ```bash\n   sudo apt install certbot python3-certbot-apache\n   sudo certbot --apache -d debian.yourdomain.com\n   ```\n\n### Option 2: Nginx Web Server\n\n1. Install Nginx:\n   ```bash\n   sudo apt install nginx\n   ```\n\n2. Configure a server block:\n   ```bash\n   sudo nano /etc/nginx/sites-available/debian-repo\n   ```\n\n   Add the following configuration:\n   ```nginx\n   server {\n       listen 80;\n       server_name debian.yourdomain.com;\n       root /path/to/debian-repo/apt;\n       \n       location / {\n           autoindex on;\n           try_files $uri $uri/ =404;\n       }\n   }\n   ```\n\n3. Enable the site and restart Nginx:\n   ```bash\n   sudo ln -s /etc/nginx/sites-available/debian-repo /etc/nginx/sites-enabled/\n   sudo systemctl restart nginx\n   ```\n\n4. Set up HTTPS with Let's Encrypt:\n   ```bash\n   sudo apt install certbot python3-certbot-nginx\n   sudo certbot --nginx -d debian.yourdomain.com\n   ```\n\n### Option 3: GitHub Pages\n\nGitHub Pages can also host a Debian repository, although with some limitations. It's a great free option for small repositories.\n\n1. Create a GitHub repository for your Debian repository.\n\n2. Structure your repository with the following directories:\n   - `apt/` - The Debian repository\n   - `public/` - Website files (optional)\n\n3. Configure GitHub Pages in the repository settings.\n\n4. Add a workflow to automatically update the repository:\n   ```yaml\n   name: Update Repository\n\n   on:\n     push:\n       branches: [ main ]\n   \n   jobs:\n     update-repo:\n       runs-on: ubuntu-latest\n       steps:\n         - uses: actions/checkout@v3\n         \n         - name: Import GPG key\n           uses: crazy-max/ghaction-import-gpg@v5\n           with:\n             gpg_private_key: ${{ secrets.GPG_PRIVATE_KEY }}\n             passphrase: ${{ secrets.GPG_PASSPHRASE }}\n         \n         - name: Run repository update\n           run: |\n             sudo apt-get update\n             sudo apt-get install -y reprepro\n             ./generate_index.sh\n   ```\n\n5. Add a CNAME file for a custom domain.\n\n## Client Configuration\n\nOnce your repository is hosted, users can add it to their system with these commands:\n\n```bash\ncurl -sS https://debian.yourdomain.com/repository-key.asc | sudo gpg --dearmor --yes -o /etc/apt/trusted.gpg.d/yourrepo.gpg\necho \"deb https://debian.yourdomain.com $(lsb_release -sc) main\" | sudo tee /etc/apt/sources.list.d/yourrepo.list\nsudo apt update\n```\n\n## Real-world Example: My Repository\n\nMy personal Debian repository at [debian.griffo.io](https://debian.griffo.io) hosts packages for several tools that I use regularly:\n\n- [lazygit](https://github.com/jesseduffield/lazygit/) - A simple terminal UI for git commands\n- [lazydocker](https://github.com/jesseduffield/lazydocker/) - A simple terminal UI for Docker\n- [uv](https://github.com/astral-sh/uv) - A fast Python package installer and resolver\n- [yazi](https://github.com/sxyazi/yazi/) - A terminal file manager \n- [lowfi](https://github.com/talwat/lowfi) - A TUI system resource monitor\n- [Ghostty](https://ghostty.org) - A fast, GPU-accelerated terminal emulator\n\nI build these packages in separate repositories:\n- [lazygit-debian](https://github.com/dariogriffo/lazygit-debian)\n- [lazydocker-debian](https://github.com/dariogriffo/lazydocker-debian)\n- [uv-debian](https://github.com/dariogriffo/uv-debian)\n- [yazi-debian](https://github.com/dariogriffo/yazi-debian)\n- [lowfi-debian](https://github.com/dariogriffo/lowfi-debian)\n- [ghostty-debian](https://github.com/dariogriffo/ghostty-debian)\n\nThe repository itself is maintained in the [debian.griffo.io](https://github.com/dariogriffo/debian.griffo.io) GitHub repository.\n\n## Common Issues and Troubleshooting\n\n### GPG Key Problems\n\nIf users report issues with GPG keys, ensure:\n- Your public key is publicly available\n- The InRelease and Release.gpg files are properly signed\n- Users are correctly importing the key\n\n### Package Conflicts\n\nIf a package conflicts with an official package:\n- Consider using a different package name (e.g., `myapp-unofficial`)\n- Use a different version scheme that won't conflict with official packages\n\n### Repository Updates Not Visible\n\nIf updates don't appear for users:\n- Verify the Release files have updated dates and checksums\n- Check that files are accessible over HTTP/HTTPS\n- Confirm package architecture matches what users are expecting\n\n## Advanced Topics\n\n### Setting Up Multiple Components\n\nComponents (like main, contrib, non-free) can be configured in the `distributions` file:\n\n```\nComponents: main contrib non-free\n```\n\nThen, when adding packages, specify the component:\n\n```bash\nreprepro -C contrib includedeb bookworm package.deb\n```\n\n### Creating Multiple Repositories\n\nFor completely separate repositories (e.g., stable vs. testing), create separate `distributions` files:\n\n```bash\nmkdir -p reprepro/{stable,testing}/conf\n```\n\nEach with its own configuration.\n\n## Conclusion\n\nSetting up a personal Debian repository might seem complicated at first, but tools like `reprepro` make the process much more manageable. Once you understand the structure and have the initial setup complete, maintaining the repository becomes quite straightforward.\n\nBy following this guide, you can create your own repository to distribute your Debian packages professionally. Whether you're distributing software for a small team or sharing your open-source projects with the world, a proper Debian repository provides a professional and convenient installation experience.\n\nFor more details about my personal Debian repository and the packages I maintain, visit [debian.griffo.io](https://debian.griffo.io) or check out the [GitHub repository](https://github.com/dariogriffo/debian.griffo.io).\n\nIf you're looking for information on creating the actual Debian packages, don't miss my [previous guide on Debian packaging](/posts/ultimate-guide-debian-packaging/)."}]